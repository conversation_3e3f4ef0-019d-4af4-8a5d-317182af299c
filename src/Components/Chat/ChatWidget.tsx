import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  IoChatbubbleEllipsesOutline,
  IoClose,
  IoSearch,
  IoPeople,
  IoSend,
} from "react-icons/io5";
import { FaUser<PERSON>riends, FaRobot } from "react-icons/fa";
import { BsChatDots, BsCircleFill } from "react-icons/bs";
import { getConnections } from "../../store/actioncreators/connectionactions";
import { getChat, sendChat } from "../../store/actioncreators/chatactions";
import { sendchatItem } from "../constants";
import defaultPic from "../../assests/images/default-user.svg";
import "./FloatingChat.css";

interface Message {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  options?: string[];
}

type ChatView = "home" | "chat" | "connections" | "user-chat";

const ChatWidget: React.FC = () => {
  const dispatch: any = useDispatch();
  const location = useLocation();
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const connections = useSelector(
    (state: STATE) => state.CONNECTION.CONNECTION.CONNECTION_LIST.connections
  );
  const chatData = useSelector((state: STATE) => state.CHAT.CHAT_LIST.CHAT);

  // Chat widget states
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [hoveredConnection, setHoveredConnection] = useState<string | null>(
    null
  );

  // User-to-user chat states
  const [selectedConnection, setSelectedConnection] = useState<any>(null);
  const [userChatMessages, setUserChatMessages] = useState<any[]>([]);
  const [userChatInput, setUserChatInput] = useState("");

  // Chatbot states
  const [currentView, setCurrentView] = useState<ChatView>("home");
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const isSignedIn = currentUser.admin !== -1;

  useEffect(() => {
    if (isSignedIn) {
      dispatch(getConnections());
    }
  }, [dispatch, isSignedIn]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    if (isExpanded && inputRef.current && currentView === "chat") {
      inputRef.current.focus();
    }
  }, [isExpanded, currentView]);

  // Update user chat messages when chat data changes
  useEffect(() => {
    if (chatData && selectedConnection && currentView === "user-chat") {
      setUserChatMessages(chatData);
    }
  }, [chatData, selectedConnection, currentView]);

  // Filter connections based on search term
  const filteredConnections = connections.filter((connection: any) => {
    const connectionUser =
      connection.sender?._id === currentUser.id
        ? connection.receiver
        : connection.sender;
    return connectionUser?.fullName
      ?.toLowerCase()
      .includes(searchTerm.toLowerCase());
  });

  const handleConnectionClick = (connection: any) => {
    const connectionUser =
      connection.sender?._id === currentUser.id
        ? connection.receiver
        : connection.sender;

    if (connectionUser && connectionUser._id) {
      setSelectedConnection(connectionUser);
      setCurrentView("user-chat");
      // Load existing chat messages for this connection
      dispatch(getChat(connectionUser._id, "0", "20"));
      setUserChatMessages([]); // Reset messages, will be populated by Redux
    }
  };

  // Chatbot predefined responses
  const predefinedResponses: {
    [key: string]: { text: string; options?: string[] };
  } = {
    "technical support": {
      text: "I can help you with technical issues! Here are some common solutions:\n\n• Clear your browser cache and cookies\n• Try using a different browser\n• Check your internet connection\n• Update your browser to the latest version\n\nIf you're still experiencing issues, please contact our technical <NAME_EMAIL>",
      options: ["Account Questions", "General Information", "Contact Support"],
    },
    "account questions": {
      text: "For account-related queries, I can help with:\n\n• Profile setup and updates\n• Password reset instructions\n• Account verification issues\n• Subscription and billing questions\n\nWhat specific account issue can I assist you with?",
      options: ["Technical Support", "General Information", "Contact Support"],
    },
    "general information": {
      text: "GTI (Global Technology Inroads) connects businesses worldwide for technology partnerships and collaborations. We offer:\n\n• Technology partnership opportunities\n• Business networking platform\n• Innovation showcasing\n• Global market access\n\nWhat would you like to know more about?",
      options: ["Technical Support", "Account Questions", "Contact Support"],
    },
    "contact support": {
      text: "You can reach our support team through:\n\n📧 Email: <EMAIL>\n📞 Phone: Available on our contact page\n💬 Live Chat: Right here!\n\nOur team typically responds within 24 hours. How else can I help you today?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
    hello: {
      text: "Hello! Welcome to GTI Helpdesk. I'm here to assist you with any questions about our platform, technical issues, or general information. How can I help you today?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    hi: {
      text: "Hi there! I'm your GTI assistant. I can help you with technical support, account questions, or provide information about our services. What would you like to know?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
    help: {
      text: "I'm here to help! I can assist you with:\n• Technical issues and troubleshooting\n• Account and profile questions\n• Information about GTI services\n• Connecting you with our support team\n\nWhat do you need help with?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    default: {
      text: "I understand you're looking for help. Let me connect you with the right resources. You can also contact our support team <NAME_EMAIL>",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
  };

  const generateBotResponse = (
    userMessage: string
  ): { text: string; options?: string[] } => {
    const lowerMessage = userMessage.toLowerCase().trim();

    // Direct matches for predefined responses
    for (const [key, response] of Object.entries(predefinedResponses)) {
      if (key !== "default" && lowerMessage.includes(key.toLowerCase())) {
        return response;
      }
    }

    // Keyword matching
    const keywordMatches = [
      {
        keywords: ["technical", "bug", "error", "issue", "problem"],
        response: "technical support",
      },
      {
        keywords: ["account", "profile", "login", "password", "signup"],
        response: "account questions",
      },
      {
        keywords: ["info", "about", "what", "how", "explain"],
        response: "general information",
      },
      {
        keywords: ["contact", "support", "help", "email", "phone"],
        response: "contact support",
      },
      { keywords: ["hello", "hi", "hey"], response: "hello" },
    ];

    for (const match of keywordMatches) {
      if (match.keywords.some((keyword) => lowerMessage.includes(keyword))) {
        return predefinedResponses[match.response];
      }
    }

    return predefinedResponses.default;
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    setCurrentView("chat");

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isBot: false,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsTyping(true);

    setTimeout(() => {
      const botResponse = generateBotResponse(text);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: botResponse.text,
        isBot: true,
        timestamp: new Date(),
        options: botResponse.options,
      };

      setMessages((prev) => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleNavigation = (view: ChatView) => {
    setCurrentView(view);
    if (view === "chat" && messages.length === 0) {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        text: "Hi there! You're speaking with an AI Agent.\nI'm here to answer your questions.\n\nPlease provide context on your project/use-case so I can assist best.",
        isBot: true,
        timestamp: new Date(),
      };
      setMessages([welcomeMessage]);
    }
  };

  const handleOptionClick = (option: string) => {
    handleSendMessage(option);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(inputValue);
  };

  // User chat handlers
  const handleUserChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userChatInput.trim() || !selectedConnection) return;

    const messageData: sendchatItem = {
      receiver: selectedConnection._id,
      message: userChatInput.trim(),
    };

    // Send message via Redux action
    dispatch(sendChat(messageData, 0, 20));

    // Add message to local state for immediate UI update
    const newMessage = {
      _id: Date.now().toString(),
      message: userChatInput.trim(),
      senderId: currentUser.id,
      receiverId: selectedConnection._id,
      createdAt: new Date().toISOString(),
    };

    setUserChatMessages((prev) => [...prev, newMessage]);
    setUserChatInput("");
  };

  const handleBackToConnections = () => {
    setCurrentView("connections");
    setSelectedConnection(null);
    setUserChatMessages([]);
  };

  const toggleChat = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setShowWelcome(false);
      // Reset to appropriate view based on user state
      if (isSignedIn) {
        setCurrentView("connections");
      } else {
        setCurrentView("home");
      }
    } else {
      // Reset states when closing
      setSelectedConnection(null);
      setUserChatMessages([]);
    }
  };

  return (
    <>
      {/* Main Chat Widget */}
      <div className={`fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-[9999]`}>
        {/* Chat Panel */}
        {isExpanded && (
          <div className="mb-4 w-96 max-w-[calc(100vw-2rem)] h-[38rem] max-h-[calc(100vh-6rem)] bg-gradient-to-br from-GTI-BLUE-default via-blue-700 to-blue-800 rounded-3xl shadow-2xl border border-blue-400/20 flex flex-col animate-fade-in-up overflow-hidden backdrop-blur-xl">
            {/* Header */}
            <div className="p-6 pb-4 relative bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-white font-bold text-xl tracking-wider drop-shadow-lg">
                    GTI
                  </div>
                  <div className="text-white/80 text-sm">
                    {isSignedIn ? "Chat & Support" : "Support"}
                  </div>
                </div>
                <button
                  onClick={toggleChat}
                  className="text-white/70 hover:text-white transition-colors p-1"
                >
                  <IoClose className="w-5 h-5" />
                </button>
              </div>

              {/* Navigation Tabs for signed-in users */}
              {isSignedIn && (
                <div className="flex mt-4 space-x-1 bg-white/10 rounded-lg p-1">
                  <button
                    onClick={() => setCurrentView("connections")}
                    className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all duration-200 ${
                      currentView === "connections"
                        ? "bg-white text-GTI-BLUE-default shadow-sm"
                        : "text-white/80 hover:text-white hover:bg-white/10"
                    }`}
                  >
                    <IoPeople className="w-4 h-4 inline mr-2" />
                    Connections
                  </button>
                  <button
                    onClick={() => handleNavigation("chat")}
                    className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all duration-200 ${
                      currentView === "chat"
                        ? "bg-white text-GTI-BLUE-default shadow-sm"
                        : "text-white/80 hover:text-white hover:bg-white/10"
                    }`}
                  >
                    <FaRobot className="w-4 h-4 inline mr-2" />
                    Support Bot
                  </button>
                </div>
              )}

              {/* Welcome message for non-signed-in users */}
              {!isSignedIn && (
                <div className="mt-8">
                  <h2 className="text-white text-2xl font-bold mb-3 drop-shadow-lg">
                    Hi there! 👋
                  </h2>
                  <p className="text-white/95 text-lg font-medium drop-shadow">
                    How can we help you today?
                  </p>
                </div>
              )}
            </div>

            {/* Content Area */}
            <div className="flex-1 overflow-hidden">
              {/* Connections View (for signed-in users) */}
              {isSignedIn && currentView === "connections" && (
                <div className="h-full flex flex-col">
                  {/* Search */}
                  <div className="p-4 border-b border-white/10">
                    <div className="relative">
                      <IoSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                      <input
                        type="text"
                        placeholder="Search connections..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 min-w-0 flex-1"
                      />
                    </div>
                  </div>

                  {/* Connections List */}
                  <div className="flex-1 overflow-y-auto">
                    {filteredConnections.length > 0 ? (
                      filteredConnections.map((connection: any) => {
                        const connectionUser =
                          connection.sender?._id === currentUser.id
                            ? connection.receiver
                            : connection.sender;
                        const isCurrentlySelected =
                          selectedConnection?._id === connectionUser?._id;
                        const isHovered =
                          hoveredConnection === connectionUser?._id;

                        return (
                          <div
                            key={connectionUser?._id}
                            onClick={() => handleConnectionClick(connection)}
                            onMouseEnter={() =>
                              setHoveredConnection(connectionUser?._id)
                            }
                            onMouseLeave={() => setHoveredConnection(null)}
                            className={`flex items-center gap-3 p-4 cursor-pointer border-b border-white/10 last:border-b-0 transition-all duration-200 ${
                              isCurrentlySelected
                                ? "bg-white/20 border-l-4 border-l-white"
                                : isHovered
                                ? "bg-white/10 transform translate-x-1"
                                : "hover:bg-white/10"
                            }`}
                          >
                            <div className="relative">
                              <img
                                src={connectionUser?.profileImage || defaultPic}
                                alt={connectionUser?.fullName || "User"}
                                className={`w-12 h-12 rounded-full object-cover border-2 transition-all duration-200 ${
                                  isCurrentlySelected
                                    ? "border-white shadow-lg"
                                    : "border-white/50 shadow-md"
                                }`}
                              />
                              <div className="absolute -bottom-1 -right-1">
                                <BsCircleFill className="w-4 h-4 text-green-400" />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <p
                                  className={`font-medium text-sm truncate ${
                                    isCurrentlySelected
                                      ? "text-white"
                                      : "text-white/90"
                                  }`}
                                >
                                  {connectionUser?.fullName || "Unknown User"}
                                </p>
                                {isHovered && !isCurrentlySelected && (
                                  <BsChatDots className="w-3 h-3 text-white/80" />
                                )}
                              </div>
                              <p
                                className={`text-xs truncate ${
                                  isCurrentlySelected
                                    ? "text-white/90 font-medium"
                                    : "text-white/70"
                                }`}
                              >
                                {isCurrentlySelected
                                  ? "💬 Currently chatting"
                                  : isHovered
                                  ? "Click to start chatting"
                                  : "Available to chat"}
                              </p>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full text-white/70">
                        <FaUserFriends className="w-12 h-12 mb-4" />
                        <p className="text-center">
                          {searchTerm
                            ? "No connections found"
                            : "No connections yet"}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Chatbot Home View (for non-signed-in users or when chatbot is selected) */}
              {currentView === "home" && (
                <div className="px-6 pb-6 space-y-4">
                  {/* Ask a Question Card */}
                  <button
                    onClick={() =>
                      handleSendMessage("I need help with something")
                    }
                    className="p-6 bg-gradient-to-r from-white/10 to-white/5 rounded-2xl border border-white/20 hover:border-white/30 transition-all duration-300 hover:shadow-lg hover:scale-[1.02] group"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <IoChatbubbleEllipsesOutline className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-left">
                        <h3 className="text-white font-semibold text-lg">
                          Ask a Question
                        </h3>
                        <p className="text-white/70 text-sm">
                          Get instant help from our AI assistant
                        </p>
                      </div>
                    </div>
                  </button>

                  {/* Quick Actions */}
                  <div className="grid grid-cols-2 gap-3">
                    {[
                      {
                        title: "Technical Support",
                        icon: "🔧",
                        action: "Technical Support",
                      },
                      {
                        title: "Account Help",
                        icon: "👤",
                        action: "Account Questions",
                      },
                      {
                        title: "General Info",
                        icon: "ℹ️",
                        action: "General Information",
                      },
                      {
                        title: "Contact Us",
                        icon: "📞",
                        action: "Contact Support",
                      },
                    ].map((item, index) => (
                      <button
                        key={index}
                        onClick={() => handleSendMessage(item.action)}
                        className="p-4 bg-white/5 rounded-xl border border-white/10 hover:border-white/20 hover:bg-white/10 transition-all duration-300 hover:scale-105 group"
                      >
                        <div className="text-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </div>
                        <p className="text-white text-sm font-medium">
                          {item.title}
                        </p>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* User Chat View */}
              {currentView === "user-chat" && selectedConnection && (
                <div className="flex flex-col h-full">
                  {/* Chat Header */}
                  <div className="p-6 pb-4 border-b border-white/10">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={handleBackToConnections}
                        className="text-white/70 hover:text-white transition-colors"
                      >
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 19l-7-7 7-7"
                          />
                        </svg>
                      </button>
                      <div className="relative">
                        <img
                          src={selectedConnection.profileImage || defaultPic}
                          alt={selectedConnection.fullName}
                          className="w-8 h-8 rounded-full object-cover border-2 border-white/50"
                        />
                        <div className="absolute -bottom-1 -right-1">
                          <BsCircleFill className="w-3 h-3 text-green-400" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-white font-semibold">
                          {selectedConnection.fullName}
                        </h3>
                        <p className="text-white/70 text-sm">Active now</p>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {userChatMessages.length === 0 ? (
                      <div className="flex flex-col items-center justify-center h-full text-white/70">
                        <IoChatbubbleEllipsesOutline className="w-12 h-12 mb-4" />
                        <p className="text-center">
                          Start a conversation with{" "}
                          {selectedConnection.fullName}
                        </p>
                      </div>
                    ) : (
                      userChatMessages.map((message, index) => {
                        const isOwnMessage =
                          message.senderId === currentUser.id;
                        return (
                          <div
                            key={message._id || index}
                            className={`flex ${
                              isOwnMessage ? "justify-end" : "justify-start"
                            }`}
                          >
                            <div
                              className={`max-w-[80%] ${
                                isOwnMessage ? "order-1" : "order-2"
                              }`}
                            >
                              {!isOwnMessage && (
                                <div className="flex items-center space-x-2 mb-2">
                                  <img
                                    src={
                                      selectedConnection.profileImage ||
                                      defaultPic
                                    }
                                    alt={selectedConnection.fullName}
                                    className="w-6 h-6 rounded-full object-cover"
                                  />
                                  <span className="text-white/70 text-xs">
                                    {selectedConnection.fullName}
                                  </span>
                                </div>
                              )}
                              <div
                                className={`p-3 rounded-2xl ${
                                  isOwnMessage
                                    ? "bg-white text-GTI-BLUE-default ml-auto"
                                    : "bg-white/10 text-white border border-white/20"
                                }`}
                              >
                                <p className="text-sm">{message.message}</p>
                              </div>
                              <div
                                className={`text-xs text-white/50 mt-1 ${
                                  isOwnMessage ? "text-right" : "text-left"
                                }`}
                              >
                                {new Date(message.createdAt).toLocaleTimeString(
                                  [],
                                  {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  }
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })
                    )}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Input */}
                  <div className="p-4 border-t border-white/10">
                    <form
                      onSubmit={handleUserChatSubmit}
                      className="flex space-x-2"
                    >
                      <input
                        type="text"
                        value={userChatInput}
                        onChange={(e) => setUserChatInput(e.target.value)}
                        placeholder={`Message ${selectedConnection.fullName}...`}
                        className="p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 min-w-0 flex-1"
                      />
                      <button
                        type="submit"
                        disabled={!userChatInput.trim()}
                        className="p-3 bg-white text-GTI-BLUE-default rounded-lg hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        <IoSend className="w-4 h-4" />
                      </button>
                    </form>
                  </div>
                </div>
              )}

              {/* Chatbot Chat View */}
              {currentView === "chat" && (
                <div className="flex flex-col h-full">
                  {/* Chat Header */}
                  <div className="p-6 pb-4 border-b border-white/10">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() =>
                          setCurrentView(isSignedIn ? "connections" : "home")
                        }
                        className="text-white/70 hover:text-white transition-colors"
                      >
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 19l-7-7 7-7"
                          />
                        </svg>
                      </button>
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">
                          GTI
                        </span>
                      </div>
                      <div>
                        <h3 className="text-white font-semibold">GTI Bot</h3>
                        <p className="text-white/70 text-sm">
                          Always here to help
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          message.isBot ? "justify-start" : "justify-end"
                        }`}
                      >
                        <div
                          className={`max-w-[80%] ${
                            message.isBot ? "order-2" : "order-1"
                          }`}
                        >
                          {message.isBot && (
                            <div className="flex items-center space-x-2 mb-2">
                              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs font-bold">
                                  GTI
                                </span>
                              </div>
                              <span className="text-white/70 text-xs">
                                GTI Bot
                              </span>
                            </div>
                          )}
                          <div
                            className={`p-3 rounded-2xl ${
                              message.isBot
                                ? "bg-white/10 text-white border border-white/20"
                                : "bg-white text-GTI-BLUE-default ml-auto"
                            }`}
                          >
                            <p className="text-sm whitespace-pre-line">
                              {message.text}
                            </p>
                          </div>
                          {message.options && (
                            <div className="mt-3 space-y-2">
                              {message.options.map((option, index) => (
                                <button
                                  key={index}
                                  onClick={() => handleOptionClick(option)}
                                  className="block text-left p-2 bg-white/5 hover:bg-white/10 border border-white/20 hover:border-white/30 rounded-lg text-white text-sm transition-all duration-200"
                                >
                                  {option}
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    {isTyping && (
                      <div className="flex justify-start">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">
                              GTI
                            </span>
                          </div>
                          <span className="text-white/70 text-xs">
                            GTI Bot is typing...
                          </span>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Input */}
                  <div className="p-4 border-t border-white/10">
                    <form onSubmit={handleSubmit} className="flex space-x-2">
                      <input
                        ref={inputRef}
                        type="text"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder="Type your message..."
                        className="p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 min-w-0 flex-1"
                      />
                      <button
                        type="submit"
                        disabled={!inputValue.trim()}
                        className="p-3 bg-white text-GTI-BLUE-default rounded-lg hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        <IoSend className="w-4 h-4" />
                      </button>
                    </form>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Chat Widget Button */}
        <div className="relative">
          <div
            onClick={toggleChat}
            className="bg-gradient-to-r from-GTI-BLUE-default to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-4 rounded-full shadow-xl cursor-pointer floating-chat-widget chat-button transform transition-all duration-300 hover:scale-110"
          >
            {isExpanded ? (
              <IoClose className="w-6 h-6" />
            ) : (
              <IoChatbubbleEllipsesOutline className="w-6 h-6" />
            )}
          </div>

          {/* Notification Badge - Can be enhanced later with unread message count */}
          {!isExpanded && false && (
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg notification-badge">
              1
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ChatWidget;
