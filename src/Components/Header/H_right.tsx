import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { IoIosNotifications } from "react-icons/io";
import { useSelector, useDispatch } from "react-redux";

import { Dispatch } from "redux";
import * as ROUTE from "../Constants/routes";
import { notificationCount } from "../../api/user";
import { HOME, NOTIFICATIONS } from "../constants";
import { resetUser } from "../../store/actioncreators/actionCreators";

interface NavbarInterface {
  handleLoginModal: () => void;
  handleNotificationModal: () => void;
  show?: boolean;
  handleShow: () => void;
}

const H_Right: React.FC<NavbarInterface> = ({
  handleLoginModal,
  handleNotificationModal,
  show,
  handleShow,
}) => {
  const [sign_modal, setSignModal] = useState(1);
  const dispatch: Dispatch<any> = useDispatch();
  const location = useLocation();

  const resetUserDetails = React.useCallback(
    () => dispatch(resetUser(currentUser)),
    [dispatch]
  );

  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);

  const [notificationCnt, setNotificationCnt] = useState(0);
  let navigate = useNavigate();
  const handleLogOut = () => {
    resetUserDetails();
    localStorage.removeItem("GTI_data");
    navigate(process.env.REACT_APP_HOME!);
  };

  const handleProfileClick = () => {
    navigate(ROUTE.PROFILE);
  };
  const handleSignSelectModal = (index: any) => {
    if (index === 1) {
      setSignModal(1);
      handleLoginModal();
    } else {
      setSignModal(2);
    }
  };
  const handleView = () => {
    navigate(NOTIFICATIONS);
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      const res = await notificationCount();
      setNotificationCnt(res?.data);
    } catch (err) {}
  };

  return (
    <div className="b2match-user-actions">
      {currentUser.admin !== -1 ? (
        <div className="b2match-user-menu">
          <button
            className="b2match-notification-button"
            onClick={() => handleView()}
            aria-label="View notifications"
          >
            <IoIosNotifications className="b2match-notification-icon" />
            {notificationCnt > 0 && (
              <span className="b2match-notification-badge">
                {notificationCnt > 99 ? "99+" : notificationCnt}
              </span>
            )}
          </button>
          <button
            className="b2match-profile-button"
            onClick={handleProfileClick}
            aria-label="Go to user profile"
          >
            <img
              src={currentUser?.company[0]?.logo || currentUser.user.profileImg}
              className="b2match-profile-avatar"
              alt={currentUser.user.name}
            />
            <span className="b2match-profile-name">
              {currentUser.user.name}
            </span>
          </button>
        </div>
      ) : (
        <div className="b2match-auth-actions">
          <button
            onClick={() => {
              handleSignSelectModal(1);
              if (show) handleShow();
            }}
            className="b2match-get-started-button"
          >
            Sign In
          </button>
        </div>
      )}
    </div>
  );
};

export default H_Right;
