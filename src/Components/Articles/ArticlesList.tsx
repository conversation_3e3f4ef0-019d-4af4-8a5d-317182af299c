import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  CalendarIcon,
  ClockIcon,
  ArrowRightIcon,
  BookOpenIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import { PlayIcon } from "@heroicons/react/24/solid";
import articlebanner from "../../assests/banners/articlebanner.png";
import ReactPlayer from "react-player";
import { articleItemFetched, LIMIT, NONE } from "../constants";
import { getArticles } from "../../store/actioncreators/articleactions";
import { useNavigate } from "react-router-dom";
import { getQueryParams } from "../../utils";
import { ArticleGridSkeleton } from "./ArticleSkeletons";
// import { InteractiveCard, InteractiveButton } from "../ui";
// import "../../styles/animations.css";

const Card = ({ item, secId }: { item: articleItemFetched; secId: string }) => {
  const DOC = new Date(item.createdAt);
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();

  const handleView = () => {
    navigate(`/articles/${item._id}`, { state: { id: item._id } });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getReadingTime = (description: string) => {
    const wordsPerMinute = 200;
    const words = description.replace(/<[^>]*>/g, "").split(" ").length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  };

  return (
    <article
      className="group relative bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:shadow-GTI-BLUE-default/10 transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
      onClick={handleView}
    >
      {/* Image/Video Container */}
      <div className="relative aspect-[16/10] overflow-hidden bg-gray-100">
        {!item.youtubeLink || item.youtubeLink === "none" ? (
          <>
            <img
              src={item?.imageUrl ?? articlebanner}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              alt={item.topic}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </>
        ) : (
          <div className="relative w-full h-full">
            <ReactPlayer
              url={item.youtubeLink === NONE ? articlebanner : item.youtubeLink}
              className="w-full h-full"
              width="100%"
              height="100%"
              light
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
                <PlayIcon className="w-6 h-6 text-GTI-BLUE-default" />
              </div>
            </div>
          </div>
        )}

        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 backdrop-blur-sm text-GTI-BLUE-default border border-white/20">
            Article
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Meta Information */}
        <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
          <div className="flex items-center gap-1">
            <CalendarIcon className="w-4 h-4" />
            <time>{formatDate(DOC)}</time>
          </div>
          <div className="flex items-center gap-1">
            <ClockIcon className="w-4 h-4" />
            <span>
              {getReadingTime(item.description || item.shortDescription)}
            </span>
          </div>
        </div>

        {/* Title */}
        <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
          <span dangerouslySetInnerHTML={{ __html: item.topic }} />
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
          {item.shortDescription}
        </p>

        {/* Read More Link */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-GTI-BLUE-default font-medium text-sm group-hover:gap-2 transition-all duration-200">
            <span>Read Article</span>
            <ArrowRightIcon className="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-200" />
          </div>
        </div>
      </div>

      {/* Hover Effect Border */}
      <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-GTI-BLUE-default/20 transition-colors duration-300 pointer-events-none"></div>
    </article>
  );
};

const ArticlesList = ({
  skip: parentSkip,
  limit: parentLimit,
  secId,
}: {
  skip: string;
  limit: string;
  secId: string;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const articles: ARTICLE = useSelector(
    (state: STATE) => state.ARTICLE.ARTICLE
  );

  const [loadMore, setMore] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const [page, setPage] = useState({
    skip: "0",
    limit: LIMIT,
  });
  const skip = getQueryParams("skip") ?? "0";
  const [maxSkip, setMaxSkip] = useState(0);

  const fetchData = (val: number) => {
    let newSkip = parseInt(page.skip) + val;
    if (newSkip >= 0) {
      setIsLoading(true);
      navigate(`/articles?skip=${newSkip}`);
      setPage({
        skip: newSkip.toString(),
        limit: page.limit,
      });
      dispatch(getArticles(secId, newSkip.toString(), page.limit));
      // Simulate loading time for better UX
      setTimeout(() => setIsLoading(false), 500);
    }
  };

  useEffect(() => {
    setMaxSkip(Math.ceil(articles.articlesCount / 9));
  }, [page, articles]);

  useEffect(() => {
    setIsLoading(true);
    dispatch(getArticles(secId, skip, page.limit));
    setPage({
      skip: skip ? skip : "0",
      limit: page.limit,
    });
    window.scrollTo(0, 0);
    // Simulate loading time for better UX
    setTimeout(() => setIsLoading(false), 800);
  }, [skip, secId, dispatch, page.limit]);

  // Show skeleton loading state
  if (isLoading) {
    return <ArticleGridSkeleton count={9} />;
  }

  return (
    <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 pb-16">
      {/* Articles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8 stagger-children">
        {articles.articles.map((item: articleItemFetched, id: number) => {
          return <Card item={item} key={id} secId={secId} />;
        })}
      </div>

      {/* Empty State */}
      {articles.articles.length === 0 && !isLoading && (
        <div className="text-center py-16">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <BookOpenIcon className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No articles found
          </h3>
          <p className="text-gray-500">
            Try adjusting your filter or check back later for new content.
          </p>
        </div>
      )}
      {/* Modern Pagination */}
      {maxSkip > 1 && (
        <div className="flex flex-col items-center space-y-4 mt-12">
          {/* Results Info */}
          <div className="text-sm text-gray-600">
            Showing page {parseInt(page.skip) + 1} of {maxSkip}
            <span className="mx-2">•</span>
            {articles.articlesCount} total articles
          </div>

          {/* Pagination Controls */}
          <nav className="flex items-center space-x-2" aria-label="Pagination">
            {/* Previous Button */}
            <button
              disabled={page.skip === "0"}
              onClick={() => fetchData(-1)}
              className="px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"
            >
              <ChevronLeftIcon className="w-4 h-4" />
              <span>Previous</span>
            </button>

            {/* Page Numbers */}
            <div className="flex items-center space-x-1">
              {/* First page */}
              {parseInt(page.skip) > 2 && (
                <>
                  <button
                    onClick={() => fetchData(-parseInt(page.skip))}
                    className="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-GTI-BLUE-default hover:text-white hover:border-GTI-BLUE-default transition-all duration-200"
                  >
                    1
                  </button>
                  {parseInt(page.skip) > 3 && (
                    <span className="inline-flex items-center justify-center w-10 h-10 text-gray-400">
                      ...
                    </span>
                  )}
                </>
              )}

              {/* Previous page */}
              {parseInt(page.skip) > 0 && (
                <button
                  onClick={() => fetchData(-1)}
                  className="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-GTI-BLUE-default hover:text-white hover:border-GTI-BLUE-default transition-all duration-200"
                >
                  {parseInt(page.skip)}
                </button>
              )}

              {/* Current page */}
              <button
                disabled
                className="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-white bg-GTI-BLUE-default border border-GTI-BLUE-default rounded-lg cursor-default"
              >
                {parseInt(page.skip) + 1}
              </button>

              {/* Next page */}
              {parseInt(page.skip) + 1 < maxSkip && (
                <button
                  onClick={() => fetchData(1)}
                  className="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-GTI-BLUE-default hover:text-white hover:border-GTI-BLUE-default transition-all duration-200"
                >
                  {parseInt(page.skip) + 2}
                </button>
              )}

              {/* Last page */}
              {parseInt(page.skip) + 2 < maxSkip && (
                <>
                  {parseInt(page.skip) + 3 < maxSkip && (
                    <span className="inline-flex items-center justify-center w-10 h-10 text-gray-400">
                      ...
                    </span>
                  )}
                  <button
                    onClick={() => fetchData(maxSkip - parseInt(page.skip) - 1)}
                    className="inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-GTI-BLUE-default hover:text-white hover:border-GTI-BLUE-default transition-all duration-200"
                  >
                    {maxSkip}
                  </button>
                </>
              )}
            </div>

            {/* Next Button */}
            <button
              disabled={
                (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                articles.articlesCount
              }
              onClick={() => fetchData(1)}
              className="group relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-GTI-BLUE-default/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:border-gray-300 transition-all duration-200"
            >
              Next
              <ChevronRightIcon className="w-4 h-4 ml-1" />
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default ArticlesList;
