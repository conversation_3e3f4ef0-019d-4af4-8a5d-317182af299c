/* Modern Innovation Page Styles */
.innovation-main {
  @apply flex flex-col relative justify-center md:py-10 items-center w-full;
}

/* Enhanced Grid Layouts */
.existing-main {
  @apply grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-6 lg:gap-8 w-full;
}

.applied-main {
  @apply flex flex-col w-full space-y-3 justify-center;
}

.applied-content-main {
  @apply grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-6 lg:gap-8 w-full;
}

/* Modern Card Styles */
.existing-card-main {
  @apply flex flex-col relative justify-start duration-300 space-y-2 bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-xl hover:shadow-GTI-BLUE-default/20 transform hover:-translate-y-1 transition-all cursor-pointer;
  width: 100%;
  min-height: 320px;
}

/* Button Styles */
.innovation-button {
  @apply font-medium rounded-lg text-sm px-5 py-2.5 mx-1 mb-2 focus:outline-none transition-colors duration-200;
}

.innovation-active {
  @apply text-GTI-BLUE-default hover:text-blue-700;
}

.innovation-not-active {
  @apply bg-white text-GTI-BLUE-default border-2 border-slate-200 hover:bg-gray-50;
}

/* Layout Utilities */
.innovation-div {
  @apply flex w-full;
}

.existing-item {
  @apply flex flex-col justify-center items-center;
}

.existing-item-action {
  @apply flex flex-row justify-center items-center space-x-2;
}

.existing-company-logo {
  @apply flex h-16 w-full max-w-[120px] object-contain px-3 py-2 rounded-lg;
}

/* Modal Styles */
.innovation-modal-main {
  @apply absolute z-10 top-10 flex flex-col w-2/5 h-fit items-center space-y-5 bg-white duration-200 ease-in-out border border-gray-300 rounded-2xl shadow-xl justify-center p-10;
}

.modal-input {
  @apply mb-6 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default block w-full p-2.5 transition-colors duration-200;
}

/* Hover Effects */
.hover-parent:hover .image-container > img {
  transform: scale(1.05);
  transform-origin: center;
  transition: transform 0.3s ease-in-out;
}

/* Page Layout */
.innovation-banner {
  @apply flex w-full shadow-lg justify-items-start lg:h-96 md:h-48 sm:h-24 rounded-2xl object-cover;
}

.innovation-parent-details {
  @apply flex flex-col w-full items-center;
}

.innovation-details {
  @apply flex flex-col w-4/5 px-8 py-6 lg:-translate-y-20 -translate-y-10 bg-white shadow-xl rounded-2xl border border-gray-100;
}

.innovation-group {
  @apply px-6 py-4;
}

.comp-logo {
  @apply h-20 w-40 rounded-lg object-cover shadow-sm;
}

/* Line Clamp Utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation Enhancements */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

/* Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  .stagger-children > *,
  .animate-fadeInUp {
    animation: none !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
  }
}

/* Stagger Animation for Grid Items */
.stagger-children > * {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
  opacity: 1; /* Fallback for when animation doesn't work */
}

.stagger-children > *:nth-child(1) {
  animation-delay: 0.1s;
}
.stagger-children > *:nth-child(2) {
  animation-delay: 0.2s;
}
.stagger-children > *:nth-child(3) {
  animation-delay: 0.3s;
}
.stagger-children > *:nth-child(4) {
  animation-delay: 0.4s;
}
.stagger-children > *:nth-child(5) {
  animation-delay: 0.5s;
}
.stagger-children > *:nth-child(6) {
  animation-delay: 0.6s;
}

/* Ensure cards are visible even without animation */
.stagger-children > *:nth-child(n + 7) {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Force visibility for all cards as fallback */
.stagger-children > * {
  min-height: 320px;
  visibility: visible !important;
}

/* Ensure grid container is visible */
.stagger-children {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design Patterns */
@media (max-width: 640px) {
  .innovation-modal-main {
    @apply w-11/12 p-6;
  }

  .existing-card-main {
    @apply mx-0 hover:transform-none;
    min-height: 280px;
  }

  .innovation-details {
    @apply w-11/12 px-4 py-4;
  }

  .existing-company-logo {
    @apply h-12 max-w-[100px];
  }

  /* Touch-friendly buttons */
  .innovation-button {
    @apply py-3 px-6 text-base;
  }

  /* Larger tap targets */
  .existing-item-action > * {
    @apply p-3;
  }
}

@media (max-width: 768px) {
  .existing-main,
  .applied-content-main {
    @apply grid-cols-1 gap-4;
    padding: 0 1rem;
  }

  .existing-card-main {
    @apply shadow-md;
  }

  .innovation-group {
    @apply px-4 py-3;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .existing-main,
  .applied-content-main {
    @apply grid-cols-2 gap-5;
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .existing-main,
  .applied-content-main {
    @apply grid-cols-3 gap-6;
    padding: 0 2rem;
  }
}

@media (min-width: 1280px) {
  .existing-main,
  .applied-content-main {
    @apply grid-cols-4 gap-6;
    padding: 0 2rem;
  }
}

/* Focus and Accessibility Improvements */
.innovation-button:focus,
.modal-input:focus {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

.existing-card-main:focus,
.existing-card-main:focus-within {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

/* Loading States */
.innovation-skeleton {
  @apply animate-pulse;
}

.innovation-skeleton .skeleton-image {
  @apply h-48 bg-gray-200 rounded-t-2xl;
}

.innovation-skeleton .skeleton-content {
  @apply p-6 space-y-3;
}

.innovation-skeleton .skeleton-line {
  @apply h-4 bg-gray-200 rounded;
}

.innovation-skeleton .skeleton-line-short {
  @apply h-3 bg-gray-200 rounded w-3/4;
}

/* Enhanced Status Badges */
.status-badge-active {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200;
}

.status-badge-closed {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200;
}

.status-badge-pending {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200;
}

/* Modern Filter Styles */
.innovation-filter-container {
  @apply bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8;
}

.innovation-filter-button {
  @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:ring-offset-2 transition-colors duration-200;
}

.innovation-filter-active {
  @apply bg-GTI-BLUE-default text-white border-GTI-BLUE-default hover:bg-blue-700;
}

/* Empty State Styles */
.innovation-empty-state {
  @apply flex flex-col items-center justify-center py-12 px-4 text-center;
}

.innovation-empty-icon {
  @apply w-16 h-16 text-gray-400 mb-4;
}

.innovation-empty-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.innovation-empty-description {
  @apply text-sm text-gray-500 max-w-md;
}
