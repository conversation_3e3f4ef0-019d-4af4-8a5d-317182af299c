import { Dispatch, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { Helmet } from "react-helmet";

import {
  InnovationItemFetched,
  INNOVATION_FULL_CONTENT_OPTIONAL,
  INNOVATION_FULL_CONTENT_OUTCOME,
  INNOVATION_FULL_CONTENT_POSSIBLE,
  INNOVATION_FULL_CONTENT_PREVIOUS,
  INNOVATION_FULL_CONTENT_STEP,
  INNOVATION_PARTIAL_CONTENT_BOTTOM,
  INNOVATION_PARTIAL_CONTENT_TOP,
  INNOVATION,
  title,
  metaData,
  LIMIT,
} from "../constants";
import { getCalls } from "../../store/actioncreators/innovationactions";
import { ScreenSpinner } from "../utils/loader";
import { getQueryParams } from "../../utils";
import globe from "../../assests/home/<USER>";
import "./style.css";

const Card = ({ item }: { item: InnovationItemFetched }) => {
  const DOS = new Date(item.endDate);
  const navigate = useNavigate();
  const isActive = DOS.getTime() >= new Date().getTime();

  const handleView = () => {
    navigate(`/innovation-call-view/${item._id}`, { state: { id: item._id } });
  };

  return (
    <article
      className="group relative bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:shadow-GTI-BLUE-default/10 transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
      onClick={handleView}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleView();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${item.title} innovation call`}
      style={{ opacity: 1, visibility: "visible" }}
    >
      {/* Image Section with Overlay */}
      <div className="relative aspect-[16/10] overflow-hidden bg-gray-100">
        <img
          src={item.companyLogo || item.imageUrl}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          alt={`${item.companyName} logo`}
          loading="lazy"
        />

        {/* Status Badge */}
        <div className="absolute top-3 left-3">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              isActive
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {isActive ? "Active" : "Closed"}
          </span>
        </div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      {/* Content Section */}
      <div className="p-6 flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-roboto text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
              {item.title}
            </h3>
            <p className="text-sm text-gray-600 mt-1 font-medium">
              {item.companyName}
            </p>
          </div>
        </div>

        {/* Description */}
        <div className="flex-1 mb-4">
          <div className="text-sm text-gray-600 line-clamp-3">
            {item.description.length > 120
              ? item.description.substring(0, 120) + "..."
              : item.description}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center text-sm text-gray-500">
            <svg
              className="w-4 h-4 mr-1"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clipRule="evenodd"
              />
            </svg>
            Deadline:{" "}
            {DOS.toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
              year: "numeric",
            })}
          </div>

          <div className="flex items-center">
            <svg
              className="w-4 h-4 text-GTI-BLUE-default group-hover:translate-x-1 transition-transform duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      </div>
    </article>
  );
};

const Innovation = () => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  const skip = getQueryParams("skip");
  const navigator = useNavigate();
  const [more, setMore] = useState(false);
  const [page, setPage] = useState({
    skip: skip ? skip : "0",
    limit: LIMIT,
  });
  const [maxSkip, setMaxSkip] = useState(0);

  const innovation: INNOVATION = useSelector(
    (state: STATE) => state.INNOVATION.INNOVATION
  );

  useEffect(() => {
    setMaxSkip(Math.ceil(innovation.INNOVATION_COUNT / 9));
  }, [page, innovation]);

  useEffect(() => {
    setPage({
      skip: skip ? skip : "0",
      limit: LIMIT,
    });
    dispatch(getCalls(skip ?? "0", LIMIT));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [skip]);

  const fetchData = (value: number) => {
    let final =
      parseInt(page.skip) + value < 0
        ? parseInt(page.skip)
        : parseInt(page.skip) + value;
    navigator(INNOVATION + `?skip=${final}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Helmet>
        <title>{title.INNOVATION_CALLS}</title>
        <meta
          name="description"
          key="description"
          content={metaData.INNOVATION_CALLS}
        />
        <meta name="title" key="title" content="Open Innovation Platform" />
        <meta property="og:title" content="Open Innovation Platform" />
        <meta
          property="og:description"
          content={`${INNOVATION_PARTIAL_CONTENT_TOP}`}
        />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/innovation`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Open Innovation Platform" />
        <meta name="twitter:description" content={metaData.INNOVATION_CALLS} />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content="Open Innovation Platform" />
      </Helmet>

      {/* Modern Header Section */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="flex mb-6" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <a
                  href="/"
                  className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                  </svg>
                  Home
                </a>
              </li>
              <li>
                <div className="flex items-center">
                  <svg
                    className="w-6 h-6 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                    Innovation Calls
                  </span>
                </div>
              </li>
            </ol>
          </nav>

          {/* Header Content */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center mb-6 lg:mb-0">
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 rounded-2xl shadow-lg mr-4">
                <svg
                  className="w-10 h-10 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 font-roboto">
                  Open Innovation Platform
                </h1>
                <p className="text-gray-600 mt-1 font-roboto">
                  Discover innovation challenges and collaboration opportunities
                </p>
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-GTI-BLUE-default">
                  {innovation.INNOVATION_COUNT || 0}
                </div>
                <div className="text-sm text-gray-500">Active Calls</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-GTI-BLUE-default">
                  {innovation.INNOVATION_LIST?.filter(
                    (item: InnovationItemFetched) =>
                      new Date(item.endDate).getTime() >= new Date().getTime()
                  ).length || 0}
                </div>
                <div className="text-sm text-gray-500">Open</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
          {/* Description Section */}
          <div className="mb-6">
            {!more && (
              <div className="space-y-4">
                <p className="text-gray-700 font-roboto text-sm leading-relaxed">
                  {INNOVATION_PARTIAL_CONTENT_TOP}
                </p>
                <p className="text-gray-700 font-roboto text-sm leading-relaxed">
                  {INNOVATION_PARTIAL_CONTENT_BOTTOM}
                </p>
              </div>
            )}
            {more && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Steps:
                  </h3>
                  <p className="text-gray-700 font-roboto text-sm leading-relaxed whitespace-pre-wrap">
                    {INNOVATION_FULL_CONTENT_STEP}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Optional services:
                  </h3>
                  <p className="text-gray-700 font-roboto text-sm leading-relaxed whitespace-pre-wrap">
                    {INNOVATION_FULL_CONTENT_OPTIONAL}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Previous Innovation Calls:
                  </h3>
                  <p className="text-gray-700 font-roboto text-sm leading-relaxed whitespace-pre-wrap">
                    {INNOVATION_FULL_CONTENT_PREVIOUS}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Possible forms of collaboration:
                  </h3>
                  <p className="text-gray-700 font-roboto text-sm leading-relaxed whitespace-pre-wrap">
                    {INNOVATION_FULL_CONTENT_POSSIBLE}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Outcomes:
                  </h3>
                  <p className="text-gray-700 font-roboto text-sm leading-relaxed whitespace-pre-wrap">
                    {INNOVATION_FULL_CONTENT_OUTCOME}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Toggle Button */}
          <div className="flex justify-center">
            <button
              onClick={() => setMore(!more)}
              className="inline-flex items-center px-4 py-2 border border-GTI-BLUE-default text-sm font-medium rounded-lg text-GTI-BLUE-default bg-white hover:bg-GTI-BLUE-default hover:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:ring-offset-2"
            >
              {more ? "Show Less" : "Show More"}
              <svg
                className={`ml-2 w-4 h-4 transition-transform duration-200 ${
                  more ? "rotate-180" : ""
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Innovation Calls Grid */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8">
        {spinner.SPINNER ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {Array.from({ length: 6 }, (_, index) => (
              <div key={index} className="innovation-skeleton">
                <div className="skeleton-image"></div>
                <div className="skeleton-content">
                  <div className="skeleton-line mb-2"></div>
                  <div className="skeleton-line-short mb-4"></div>
                  <div className="space-y-2">
                    <div className="skeleton-line"></div>
                    <div className="skeleton-line w-5/6"></div>
                    <div className="skeleton-line w-4/6"></div>
                  </div>
                  <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-100">
                    <div className="skeleton-line w-20"></div>
                    <div className="skeleton-line w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : innovation.INNOVATION_LIST &&
          innovation.INNOVATION_LIST.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 stagger-children min-h-[200px]">
            {innovation.INNOVATION_LIST.map(
              (item: InnovationItemFetched, id: number) => {
                return (
                  <Card item={item} key={`innovation-${item._id}-${id}`} />
                );
              }
            )}
          </div>
        ) : (
          <div className="innovation-empty-state">
            <svg
              className="innovation-empty-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
              />
            </svg>
            <h3 className="innovation-empty-title">
              No Innovation Calls Available
            </h3>
            <p className="innovation-empty-description">
              There are currently no innovation calls to display. Check back
              later for new opportunities.
            </p>
          </div>
        )}
      </div>
      {/* Modern Pagination */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              disabled={page.skip === "0"}
              onClick={() => fetchData(-1)}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              disabled={
                (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                innovation.INNOVATION_COUNT
              }
              onClick={() => fetchData(1)}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>

          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{" "}
                <span className="font-medium">
                  {parseInt(page.skip) * parseInt(page.limit) + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(
                    (parseInt(page.skip) + 1) * parseInt(page.limit),
                    innovation.INNOVATION_COUNT
                  )}
                </span>{" "}
                of{" "}
                <span className="font-medium">
                  {innovation.INNOVATION_COUNT}
                </span>{" "}
                results
              </p>
            </div>

            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  disabled={page.skip === "0"}
                  onClick={() => fetchData(-1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                {/* Page Numbers */}
                {Array.from({ length: Math.min(5, maxSkip) }, (_, i) => {
                  const pageNum = Math.max(1, parseInt(page.skip) - 2) + i;
                  if (pageNum > maxSkip) return null;

                  return (
                    <button
                      key={pageNum}
                      onClick={() =>
                        fetchData(pageNum - parseInt(page.skip) - 1)
                      }
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === parseInt(page.skip) + 1
                          ? "z-10 bg-GTI-BLUE-default border-GTI-BLUE-default text-white"
                          : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  disabled={
                    (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                    innovation.INNOVATION_COUNT
                  }
                  onClick={() => fetchData(1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Innovation;
