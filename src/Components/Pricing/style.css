/* Modern Pricing Page Styles */

/* Main Container */
.pricing-page-main {
  @apply flex flex-col relative min-h-screen w-full bg-gradient-to-br from-slate-50 via-white to-blue-50;
}

/* Hero Section */
.pricing-hero-section {
  @apply relative bg-gradient-to-r from-GTI-BLUE-default via-blue-600 to-blue-700 text-white py-20 px-4 overflow-hidden;
}

.pricing-hero-content {
  @apply max-w-4xl mx-auto text-center relative z-10;
}

.pricing-hero-badge {
  @apply inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-medium mb-6 border border-white/30;
}

.pricing-hero-title {
  @apply text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-hero-subtitle {
  @apply text-xl md:text-2xl mb-12 text-blue-100 max-w-3xl mx-auto leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

/* Billing Toggle */
.pricing-billing-toggle {
  @apply flex items-center justify-center space-x-4 bg-white/10 backdrop-blur-sm rounded-2xl p-2 max-w-sm mx-auto border border-white/20;
}

.pricing-billing-toggle span {
  @apply text-sm font-medium transition-all duration-300 px-4 py-2 rounded-xl;
}

.pricing-billing-toggle span.active {
  @apply text-GTI-BLUE-default bg-white font-semibold;
}

.pricing-toggle-switch {
  @apply relative w-16 h-8 bg-white/20 rounded-full transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-white/30;
}

.pricing-toggle-slider {
  @apply absolute top-1 left-1 w-6 h-6 bg-white rounded-full transition-all duration-300 shadow-lg;
}

.pricing-toggle-slider.yearly {
  @apply transform translate-x-8;
}

.pricing-discount-badge {
  @apply ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold;
}

/* Container */
.pricing-container {
  @apply max-w-7xl mx-auto px-4;
}

/* Pricing Cards Section */
.pricing-cards-section {
  @apply py-20 px-4;
}

.pricing-cards-grid {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto;
}

/* Pricing Cards */
.pricing-card {
  @apply bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-GTI-BLUE-default/20 transform hover:-translate-y-2 relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
}

.pricing-card-popular {
  @apply ring-4 ring-GTI-BLUE-default/20 scale-105;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(248, 250, 252, 1) 100%
  );
}

.pricing-popular-badge {
  @apply absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center space-x-2 shadow-lg;
}

/* Card Header */
.pricing-card-header {
  @apply p-8 text-center border-b border-gray-100;
}

.pricing-card-icon {
  @apply w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 rounded-2xl flex items-center justify-center text-white shadow-lg;
}

.pricing-card-title {
  @apply text-2xl font-bold text-GTI-BLUE-default mb-3;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-card-description {
  @apply text-gray-600 mb-6 leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.pricing-card-price {
  @apply flex items-baseline justify-center mb-2;
}

.pricing-price-currency {
  @apply text-2xl font-semibold text-gray-500 mr-1;
}

.pricing-price-amount {
  @apply text-5xl font-bold text-GTI-BLUE-default;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-price-period {
  @apply text-xl text-gray-500 ml-1;
}

.pricing-yearly-savings {
  @apply text-green-600 text-sm font-medium bg-green-50 px-3 py-1 rounded-full inline-block;
}

/* Card Features */
.pricing-card-features {
  @apply p-8 space-y-4 flex-1;
}

.pricing-feature {
  @apply flex items-start space-x-3;
}

.pricing-feature-icon {
  @apply flex-shrink-0 mt-0.5;
}

.pricing-feature-content {
  @apply flex-1;
}

.pricing-feature-name {
  @apply block text-gray-900 font-medium mb-1;
  font-family: "DM Sans", sans-serif;
}

.pricing-feature-value {
  @apply block text-sm text-gray-500;
}

/* Card Footer */
.pricing-card-footer {
  @apply p-8 pt-0;
}

/* Buttons */
.pricing-btn {
  @apply w-full flex items-center justify-center space-x-2 font-semibold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 shadow-lg;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-btn-primary {
  @apply bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 focus:ring-blue-300 shadow-xl hover:shadow-2xl;
}

.pricing-btn-secondary {
  @apply bg-white text-GTI-BLUE-default border-2 border-GTI-BLUE-default hover:bg-GTI-BLUE-default hover:text-white focus:ring-GTI-BLUE-default/30;
}

/* Comparison Section */
.pricing-comparison-section {
  @apply py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50;
}

.pricing-section-header {
  @apply text-center mb-16;
}

.pricing-section-title {
  @apply text-3xl md:text-4xl font-bold text-GTI-BLUE-default mb-4;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-section-subtitle {
  @apply text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.pricing-comparison-table-wrapper {
  @apply overflow-x-auto bg-white rounded-3xl shadow-xl border border-gray-200;
}

.pricing-comparison-table {
  @apply min-w-full;
}

.pricing-comparison-table table {
  @apply w-full border-collapse;
}

.pricing-comparison-table th {
  @apply text-left py-4 px-6 font-semibold;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-comparison-table td {
  @apply py-4 px-6 text-sm;
  font-family: "DM Sans", sans-serif;
}

.pricing-comparison-table tbody tr:hover {
  @apply bg-gradient-to-r from-blue-50/30 to-transparent;
}

/* FAQ Section */
.pricing-faq-section {
  @apply py-20 px-4;
}

.pricing-faq-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto;
}

.pricing-faq-item {
  @apply bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300;
}

.pricing-faq-question {
  @apply text-xl font-bold text-GTI-BLUE-default mb-4;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-faq-answer {
  @apply text-gray-600 leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

/* CTA Section */
.pricing-cta-section {
  @apply py-20 px-4 bg-gradient-to-r from-GTI-BLUE-default via-blue-600 to-blue-700 text-white;
}

.pricing-cta-content {
  @apply max-w-4xl mx-auto text-center;
}

.pricing-cta-title {
  @apply text-3xl md:text-4xl font-bold mb-4;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.pricing-cta-subtitle {
  @apply text-xl mb-8 text-blue-100 leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.pricing-cta-buttons {
  @apply flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .pricing-cards-grid {
    @apply grid-cols-1 md:grid-cols-2 gap-6;
  }

  .pricing-card-popular {
    @apply scale-100;
  }
}

@media (max-width: 768px) {
  .pricing-hero-title {
    @apply text-3xl;
  }

  .pricing-hero-subtitle {
    @apply text-lg;
  }

  .pricing-cards-grid {
    @apply grid-cols-1 gap-6;
  }

  .pricing-card-header {
    @apply p-6;
  }

  .pricing-card-features {
    @apply p-6;
  }

  .pricing-card-footer {
    @apply p-6 pt-0;
  }

  .pricing-faq-grid {
    @apply grid-cols-1;
  }

  .pricing-cta-buttons {
    @apply flex-col;
  }
}

/* Animation Enhancements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pricing-card {
  animation: fadeInUp 0.6s ease-out forwards;
}

.pricing-card:nth-child(1) {
  animation-delay: 0.1s;
}
.pricing-card:nth-child(2) {
  animation-delay: 0.2s;
}
.pricing-card:nth-child(3) {
  animation-delay: 0.3s;
}

/* Hover Effects */
.pricing-card:hover .pricing-card-icon {
  @apply transform scale-110 rotate-3;
}

.pricing-btn:hover {
  @apply shadow-2xl;
}

.pricing-btn-primary:hover {
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
}

/* Glass Effect */
.pricing-card::before {
  content: "";
  @apply absolute inset-0 bg-gradient-to-br from-white/50 to-transparent pointer-events-none rounded-3xl;
}
