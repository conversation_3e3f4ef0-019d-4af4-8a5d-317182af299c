import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  CheckIcon,
  XMarkIcon,
  StarIcon,
  SparklesIcon,
  CrownIcon,
  ArrowRightIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  AcademicCapIcon,
  ShieldCheckIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import "./style.css";

interface PricingPageProps {
  handleLoginModal: () => void;
}

const PricingPage: React.FC<PricingPageProps> = ({ handleLoginModal }) => {
  const navigate = useNavigate();
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">(
    "monthly"
  );

  const pricingPlans = [
    {
      name: "Free Plan",
      price: { monthly: 0, yearly: 0 },
      popular: false,
      description: "Perfect for getting started with basic features",
      icon: <UserGroupIcon className="w-8 h-8" />,
      features: [
        {
          name: "Online / Virtual B2B Meeting Tools",
          included: false,
          value: "No access",
        },
        {
          name: "Chatbot + 24x7 Helpdesk",
          included: true,
          value: "Limited queries",
        },
        {
          name: "Paid Promotions & Advertising",
          included: false,
          value: "Not included",
        },
        {
          name: "Smart Matching Algorithms",
          included: true,
          value: "Basic matchmaking",
        },
        {
          name: "Providing Tool Kits",
          included: true,
          value: "Standard templates",
        },
        {
          name: "Funding & Strategic Guidance",
          included: true,
          value: "Resources Access",
        },
        {
          name: "Access to Connection Requests",
          included: true,
          value: "5 requests/month",
        },
        { name: "Access to Chat", included: true, value: "Connected users" },
        { name: "Events", included: false, value: "No access" },
        { name: "Training Modules", included: false, value: "Not included" },
        {
          name: "Technology Display",
          included: true,
          value: "Limited requests",
        },
        {
          name: "Technology Requests",
          included: true,
          value: "Limited requests",
        },
        { name: "Priority Support", included: true, value: "Limited support" },
      ],
      buttonText: "Get Started Free",
      buttonStyle: "pricing-btn-secondary",
    },
    {
      name: "Basic Plan",
      price: { monthly: 49, yearly: 490 },
      popular: true,
      description: "Ideal for growing businesses and active users",
      icon: <SparklesIcon className="w-8 h-8" />,
      features: [
        {
          name: "Online / Virtual B2B Meeting Tools",
          included: true,
          value: "Limited meetings (e.g., 5/month)",
        },
        {
          name: "Chatbot + 24x7 Helpdesk",
          included: true,
          value: "AI chatbot (limited responses)",
        },
        {
          name: "Paid Promotions & Advertising",
          included: false,
          value: "Not included",
        },
        {
          name: "Smart Matching Algorithms",
          included: true,
          value: "Basic AI-based matching",
        },
        {
          name: "Providing Tool Kits",
          included: true,
          value: "Standard templates",
        },
        {
          name: "Funding & Strategic Guidance",
          included: true,
          value: "Resource access only",
        },
        {
          name: "Access to Connection Requests",
          included: true,
          value: "10 requests/month",
        },
        {
          name: "Access to Chat",
          included: true,
          value: "Connected users only",
        },
        { name: "Events", included: true, value: "Limited access" },
        { name: "Training Modules", included: true, value: "Limited access" },
        {
          name: "Technology Display",
          included: true,
          value: "Limited requests",
        },
        {
          name: "Technology Requests",
          included: true,
          value: "Limited requests",
        },
        { name: "Priority Support", included: true, value: "Standard support" },
      ],
      buttonText: "Choose Basic",
      buttonStyle: "pricing-btn-primary",
    },
    {
      name: "Pro Plan",
      price: { monthly: 99, yearly: 990 },
      popular: false,
      description: "Complete solution for enterprises and power users",
      icon: <CrownIcon className="w-8 h-8" />,
      features: [
        {
          name: "Online / Virtual B2B Meeting Tools",
          included: true,
          value: "Unlimited meetings",
        },
        {
          name: "Chatbot + 24x7 Helpdesk",
          included: true,
          value: "AI chatbot + human support",
        },
        {
          name: "Paid Promotions & Advertising",
          included: true,
          value: "Featured promotions",
        },
        {
          name: "Smart Matching Algorithms",
          included: true,
          value: "Advanced AI-powered matching",
        },
        {
          name: "Providing Tool Kits",
          included: true,
          value: "Editable & customizable templates",
        },
        {
          name: "Funding & Strategic Guidance",
          included: true,
          value: "1-on-1 mentorship sessions",
        },
        {
          name: "Access to Connection Requests",
          included: true,
          value: "25 requests/month",
        },
        {
          name: "Access to Chat",
          included: true,
          value: "Unlimited connections",
        },
        { name: "Events", included: true, value: "Complete access" },
        { name: "Training Modules", included: true, value: "Full access" },
        {
          name: "Technology Display",
          included: true,
          value: "Unlimited requests",
        },
        {
          name: "Technology Requests",
          included: true,
          value: "Unlimited requests",
        },
        {
          name: "Priority Support",
          included: true,
          value: "24x7 priority support",
        },
      ],
      buttonText: "Choose Pro",
      buttonStyle: "pricing-btn-primary",
    },
  ];

  const handlePlanSelection = (planName: string, price: number) => {
    // Here you would typically handle the plan selection
    // For now, we'll just navigate or show a modal
    console.log(`Selected plan: ${planName}, Price: $${price}`);
    // You can integrate with your existing payment system here
    handleLoginModal();
  };

  return (
    <div className="pricing-page-main">
      {/* Hero Section */}
      <section className="pricing-hero-section">
        <div className="pricing-hero-content">
          <div className="pricing-hero-badge">
            <SparklesIcon className="w-5 h-5" />
            <span>Choose Your Perfect Plan</span>
          </div>
          <h1 className="pricing-hero-title">Simple, Transparent Pricing</h1>
          <p className="pricing-hero-subtitle">
            Choose the plan that fits your needs. Upgrade or downgrade at any
            time. All plans include our core features with varying limits and
            premium add-ons.
          </p>

          {/* Billing Toggle */}
          <div className="pricing-billing-toggle">
            <span className={billingCycle === "monthly" ? "active" : ""}>
              Monthly
            </span>
            <button
              onClick={() =>
                setBillingCycle(
                  billingCycle === "monthly" ? "yearly" : "monthly"
                )
              }
              className="pricing-toggle-switch"
            >
              <div
                className={`pricing-toggle-slider ${
                  billingCycle === "yearly" ? "yearly" : ""
                }`}
              ></div>
            </button>
            <span className={billingCycle === "yearly" ? "active" : ""}>
              Yearly
              <span className="pricing-discount-badge">Save 17%</span>
            </span>
          </div>
        </div>
      </section>

      {/* Pricing Cards Section */}
      <section className="pricing-cards-section">
        <div className="pricing-container">
          <div className="pricing-cards-grid">
            {pricingPlans.map((plan, index) => (
              <div
                key={index}
                className={`pricing-card ${
                  plan.popular ? "pricing-card-popular" : ""
                }`}
              >
                {plan.popular && (
                  <div className="pricing-popular-badge">
                    <StarIcon className="w-4 h-4" />
                    Most Popular
                  </div>
                )}

                <div className="pricing-card-header">
                  <div className="pricing-card-icon">{plan.icon}</div>
                  <h3 className="pricing-card-title">{plan.name}</h3>
                  <p className="pricing-card-description">{plan.description}</p>

                  <div className="pricing-card-price">
                    <span className="pricing-price-currency">$</span>
                    <span className="pricing-price-amount">
                      {plan.price[billingCycle]}
                    </span>
                    <span className="pricing-price-period">
                      /{billingCycle === "monthly" ? "month" : "year"}
                    </span>
                  </div>

                  {billingCycle === "yearly" && plan.price.yearly > 0 && (
                    <div className="pricing-yearly-savings">
                      Save ${plan.price.monthly * 12 - plan.price.yearly} per
                      year
                    </div>
                  )}
                </div>

                <div className="pricing-card-features">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="pricing-feature">
                      <div className="pricing-feature-icon">
                        {feature.included ? (
                          <CheckIcon className="w-5 h-5 text-green-500" />
                        ) : (
                          <XMarkIcon className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                      <div className="pricing-feature-content">
                        <span className="pricing-feature-name">
                          {feature.name}
                        </span>
                        <span className="pricing-feature-value">
                          {feature.value}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="pricing-card-footer">
                  <button
                    onClick={() =>
                      handlePlanSelection(plan.name, plan.price[billingCycle])
                    }
                    className={`pricing-btn ${plan.buttonStyle}`}
                  >
                    {plan.buttonText}
                    <ArrowRightIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Comparison Section */}
      <section className="pricing-comparison-section">
        <div className="pricing-container">
          <div className="pricing-section-header">
            <h2 className="pricing-section-title">Compare All Features</h2>
            <p className="pricing-section-subtitle">
              See exactly what's included in each plan to make the best choice
              for your needs
            </p>
          </div>

          <div className="pricing-comparison-table-wrapper">
            <div className="pricing-comparison-table">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-4 px-6 font-semibold text-GTI-BLUE-default">
                      Features
                    </th>
                    <th className="text-center py-4 px-6 font-semibold text-GTI-BLUE-default">
                      Free Plan
                    </th>
                    <th className="text-center py-4 px-6 font-semibold text-GTI-BLUE-default bg-blue-50">
                      Basic Plan
                    </th>
                    <th className="text-center py-4 px-6 font-semibold text-GTI-BLUE-default">
                      Pro Plan
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {pricingPlans[0].features.map((feature, index) => (
                    <tr
                      key={index}
                      className="border-b border-gray-100 hover:bg-gray-50"
                    >
                      <td className="py-4 px-6 font-medium text-gray-900">
                        {feature.name}
                      </td>
                      <td className="py-4 px-6 text-center">
                        <span className="text-sm text-gray-600">
                          {pricingPlans[0].features[index].value}
                        </span>
                      </td>
                      <td className="py-4 px-6 text-center bg-blue-50/50">
                        <span className="text-sm text-gray-600">
                          {pricingPlans[1].features[index].value}
                        </span>
                      </td>
                      <td className="py-4 px-6 text-center">
                        <span className="text-sm text-gray-600">
                          {pricingPlans[2].features[index].value}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="pricing-faq-section">
        <div className="pricing-container">
          <div className="pricing-section-header">
            <h2 className="pricing-section-title">
              Frequently Asked Questions
            </h2>
            <p className="pricing-section-subtitle">
              Have questions? We have answers. Contact us if you need more
              information.
            </p>
          </div>

          <div className="pricing-faq-grid">
            <div className="pricing-faq-item">
              <h3 className="pricing-faq-question">
                Can I change my plan anytime?
              </h3>
              <p className="pricing-faq-answer">
                Yes, you can upgrade or downgrade your plan at any time. Changes
                will be reflected in your next billing cycle.
              </p>
            </div>
            <div className="pricing-faq-item">
              <h3 className="pricing-faq-question">Is there a free trial?</h3>
              <p className="pricing-faq-answer">
                Our Free Plan gives you access to basic features forever. You
                can upgrade to paid plans when you're ready for more features.
              </p>
            </div>
            <div className="pricing-faq-item">
              <h3 className="pricing-faq-question">
                What payment methods do you accept?
              </h3>
              <p className="pricing-faq-answer">
                We accept all major credit cards, PayPal, and bank transfers.
                All payments are processed securely.
              </p>
            </div>
            <div className="pricing-faq-item">
              <h3 className="pricing-faq-question">Do you offer refunds?</h3>
              <p className="pricing-faq-answer">
                Yes, we offer a 30-day money-back guarantee for all paid plans.
                Contact support for refund requests.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="pricing-cta-section">
        <div className="pricing-container">
          <div className="pricing-cta-content">
            <h2 className="pricing-cta-title">Ready to Get Started?</h2>
            <p className="pricing-cta-subtitle">
              Join thousands of companies already using our platform to grow
              their business
            </p>
            <div className="pricing-cta-buttons">
              <button
                onClick={() =>
                  handlePlanSelection(
                    "Basic Plan",
                    pricingPlans[1].price[billingCycle]
                  )
                }
                className="pricing-btn pricing-btn-primary"
              >
                Start Free Trial
                <ArrowRightIcon className="w-5 h-5" />
              </button>
              <button
                onClick={() => navigate("/contact-us")}
                className="pricing-btn pricing-btn-secondary"
              >
                Contact Sales
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PricingPage;
