import React, { useState } from "react";
import { Helmet } from "react-helmet";
import {
  MagnifyingGlassIcon,
  LightBulbIcon,
  CheckIcon,
  EnvelopeIcon,
} from "@heroicons/react/24/outline";

import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

import {
  premiumServiceCreate,
  premiumServicesDetails,
  title,
  metaData,
} from "../constants";
import { createPremiumService } from "../../store/actioncreators/premiumservicesactions";
import gbi_home_logo from "../../assests/home/<USER>";
import SuccessModal from "./SuccessModal";
import "./style.css";

const scouter_premium_services =
  "/images/premium-services/scouter-premium-services.svg";

const ScouterPremiumServices = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  const navigate = useNavigate();

  const [successModal, setSuccessModal] = useState<boolean>(false);
  const [state, setState] = useState("LOADING");
  const [message, setMessage] = useState("");

  const user: USER = useSelector((state: STATE) => state.USER.USER);

  const paymentHandler = async (
    amount: number,
    currency: string,
    order_id: string,
    premiumServiceId: string
  ) => {
    let options = {
      key: process.env.REACT_APP_RAZORPAY_KEY_ID,
      amount,
      currency,
      name: "GTI Market Access and Innovation Services for Technology Scouters",
      description:
        "GTI Market Access and Innovation Services for Technology Scouters",
      image: gbi_home_logo,
      order_id,
      callback_url: `${process.env.REACT_APP_BASE_API}/payments/validate`,
      prefill: {
        name: user?.user?.name,
        email: user?.user?.email,
      },
      notes: {
        premiumServiceId,
      },
      theme: {
        color: "#3399cc",
      },
    };

    const rzp = new (window as any).Razorpay(options);

    rzp.on("payment.failed", function (response: any) {
      alert("Payment Failed. Please Retry again.");
      navigate("/featured/failed");
    });

    rzp.open();
  };

  const handleSuccessModal = (
    isOpen: boolean,
    state: string,
    message: string
  ) => {
    setSuccessModal(isOpen);
    setState(state);
    setMessage(message);
  };

  const handleCreate = async (value: premiumServiceCreate) => {
    try {
      if (user?.admin !== -1) {
        handleSuccessModal(true, "LOADING", "");

        const currency = "USD";

        const { title, description, timeline, reportLength, amount } = value;

        const data = {
          title,
          description,
          timeline,
          reportLength,
          amount,
          currency,
        };

        const { order_id, premiumServiceId } = await createPremiumService(data);

        handleSuccessModal(false, "LOADING", "");

        paymentHandler(amount, currency, order_id, premiumServiceId);
      } else {
        handleLoginModal();
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className="premium-services-main">
      <Helmet>
        <title>
          {title.SCOUTER_PREMIUM_SERVICES ||
            "GTI Services for Technology Scouters"}
        </title>
        <meta
          name="description"
          key="description"
          content={
            metaData.SCOUTER_PREMIUM_SERVICES ||
            "GTI Market Access and Innovation Services for Technology Scouters"
          }
        />
        <meta
          name="title"
          key="title"
          content="GTI Services for Technology Scouters"
        />
        <meta
          property="og:title"
          content="GTI Services for Technology Scouters"
        />
        <meta
          property="og:description"
          content="GTI Market Access and Innovation Services for Technology Scouters"
        />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Hero Section */}
      <section className="premium-hero-section">
        <div className="premium-hero-background"></div>
        <div className="premium-hero-content">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="flex-1 text-left lg:text-left">
              <h1 className="premium-hero-title text-left">
                GTI Market Access & Innovation Services
              </h1>
              <p className="premium-hero-subtitle text-left">
                For Technology Scouters
              </p>
              <p className="text-lg text-blue-100 leading-relaxed max-w-3xl">
                Customized assistance for finding technology partners for
                collaborations or investments. Our technology scouting platform
                provides services for leveraging effective innovation scouting
                processes including curated technology scouting, open innovation
                challenges, technical analysis, workshops, and pitch sessions.
              </p>
            </div>
            <div className="flex-1 max-w-md">
              <div className="premium-image-container">
                <img
                  src={scouter_premium_services}
                  alt="Scouter GTI Services"
                  className="premium-image"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Services Section */}
      <section className="premium-services-section">
        <div className="premium-services-container">
          <h2 className="premium-section-title">
            Technology Scouting & Open Innovation Challenges
          </h2>
          <p className="premium-section-subtitle">
            Strategic identification of partnerships and opportunities with
            technologies available in the market to ensure your offerings are
            responsive to changing customer demands
          </p>

          <div className="premium-services-grid">
            {/* Technology Scouting */}
            <div className="premium-service-card">
              <div className="premium-service-card-header bg-gradient-to-br from-green-500 to-green-600">
                <div className="premium-service-card-icon">
                  <MagnifyingGlassIcon className="w-8 h-8" />
                </div>
                <h3 className="premium-service-card-title">
                  {premiumServicesDetails.technologyScouting.title}
                </h3>
                <p className="premium-service-card-subtitle">
                  $2,500 - 30 Days
                </p>
              </div>

              <div className="premium-service-card-content">
                <p className="premium-service-card-description">
                  {premiumServicesDetails.technologyScouting.description}
                </p>

                <div className="premium-feature-list">
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      Partner identification
                    </span>
                  </div>
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      IP & technology analysis
                    </span>
                  </div>
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      Market opportunity assessment
                    </span>
                  </div>
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      30-day delivery timeline
                    </span>
                  </div>
                </div>

                <button
                  className="premium-btn-primary"
                  onClick={() =>
                    handleCreate({
                      title: premiumServicesDetails.technologyScouting.title,
                      description:
                        premiumServicesDetails.technologyScouting.description,
                      timeline: "30",
                      amount: 2500,
                    })
                  }
                >
                  Get Started - $2,500
                </button>
              </div>
            </div>
            {/* Open Innovation Challenge */}
            <div className="premium-service-card">
              <div className="premium-service-card-header bg-gradient-to-br from-purple-500 to-purple-600">
                <div className="premium-service-card-icon">
                  <LightBulbIcon className="w-8 h-8" />
                </div>
                <h3 className="premium-service-card-title">
                  {premiumServicesDetails.openInnovationChallenge.title}
                </h3>
                <p className="premium-service-card-subtitle">
                  Custom Pricing - 3 Months
                </p>
              </div>

              <div className="premium-service-card-content">
                <p className="premium-service-card-description">
                  {premiumServicesDetails.openInnovationChallenge.description}
                </p>

                <div className="premium-feature-list">
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      Challenge design & hosting
                    </span>
                  </div>
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      Technology provider screening
                    </span>
                  </div>
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      Pitch session facilitation
                    </span>
                  </div>
                  <div className="premium-feature-item">
                    <CheckIcon className="premium-feature-icon" />
                    <span className="premium-feature-text">
                      3-month comprehensive process
                    </span>
                  </div>
                </div>

                <a
                  href="mailto:<EMAIL>"
                  className="premium-btn-email"
                >
                  <EnvelopeIcon className="w-5 h-5" />
                  Contact for Custom Quote
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Additional Services Section */}
      <section className="premium-services-section bg-white">
        <div className="premium-services-container">
          <h2 className="premium-section-title">
            Additional Value-Added Services
          </h2>
          <p className="premium-section-subtitle">
            Comprehensive support services to enhance your technology scouting
            and innovation processes
          </p>

          <div className="premium-additional-services">
            <div className="premium-additional-grid">
              <div className="premium-additional-card">
                <div className="premium-additional-number">01</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices1.title}
                </h4>
                <p className="premium-additional-text mb-4">
                  {
                    premiumServicesDetails.additionalValueAddedServices1
                      .description
                  }
                </p>
                <button
                  className="premium-btn-primary"
                  onClick={() =>
                    handleCreate({
                      title:
                        premiumServicesDetails.additionalValueAddedServices1
                          .title,
                      description:
                        premiumServicesDetails.additionalValueAddedServices1
                          .description,
                      amount: 3500,
                    })
                  }
                >
                  Get Started - $3,500
                </button>
              </div>

              <div className="premium-additional-card">
                <div className="premium-additional-number">02</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices2.title}
                </h4>
                <p className="premium-additional-text mb-4">
                  {
                    premiumServicesDetails.additionalValueAddedServices2
                      .description
                  }
                </p>
                <button
                  className="premium-btn-primary"
                  onClick={() =>
                    handleCreate({
                      title:
                        premiumServicesDetails.additionalValueAddedServices2
                          .title,
                      description:
                        premiumServicesDetails.additionalValueAddedServices2
                          .description,
                      amount: 5000,
                    })
                  }
                >
                  Get Started - $5,000
                </button>
              </div>
              <div className="premium-additional-card">
                <div className="premium-additional-number">03</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices3.title}
                </h4>
                <p className="premium-additional-text mb-4">
                  {
                    premiumServicesDetails.additionalValueAddedServices3
                      .description
                  }
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="premium-btn-email"
                >
                  <EnvelopeIcon className="w-5 h-5" />
                  Contact for Quote
                </a>
              </div>

              <div className="premium-additional-card">
                <div className="premium-additional-number">04</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices4.title}
                </h4>
                <p className="premium-additional-text mb-4">
                  {
                    premiumServicesDetails.additionalValueAddedServices4
                      .description
                  }
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="premium-btn-email"
                >
                  <EnvelopeIcon className="w-5 h-5" />
                  Custom Scope & Pricing
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="premium-services-section bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white">
        <div className="premium-services-container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Find Your Technology Partners?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Get started with our technology scouting services and discover
            innovative solutions for your business
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="premium-btn-email max-w-xs mx-auto sm:mx-0"
            >
              <EnvelopeIcon className="w-5 h-5" />
              Start Your Technology Search
            </a>
          </div>
        </div>
      </section>
      {successModal && (
        <SuccessModal
          state={state}
          message={message}
          show={successModal}
          toggle={handleSuccessModal}
        />
      )}
    </div>
  );
};

export default ScouterPremiumServices;
