import React, { useState } from "react";
import { Helmet } from "react-helmet";
import {
  ChartBarIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CheckIcon,
  ClockIcon,
  DocumentIcon,
  EnvelopeIcon,
} from "@heroicons/react/24/outline";

import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

import {
  premiumServiceCreate,
  premiumServicesDetails,
  title,
  metaData,
} from "../constants";
import { createPremiumService } from "../../store/actioncreators/premiumservicesactions";
import gbi_home_logo from "../../assests/home/<USER>";
import SuccessModal from "./SuccessModal";
import "./style.css";

const displayer_premium_services =
  "/images/premium-services/displayer-premium-services.svg";

const DisplayerPremiumServices = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  const navigate = useNavigate();

  const [successModal, setSuccessModal] = useState<boolean>(false);
  const [state, setState] = useState("LOADING");
  const [message, setMessage] = useState("");

  const user: USER = useSelector((state: STATE) => state.USER.USER);

  const [value, setValue] = useState<number>(5);
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(Number(event.target.value));
  };

  const paymentHandler = async (
    amount: number,
    currency: string,
    order_id: string,
    premiumServiceId: string
  ) => {
    let options = {
      key: process.env.REACT_APP_RAZORPAY_KEY_ID,
      amount,
      currency,
      name: "GTI Market Access and Innovation Services for Technology Scouters",
      description:
        "GTI Market Access and Innovation Services for Technology Scouters",
      image: gbi_home_logo,
      order_id,
      callback_url: `${process.env.REACT_APP_BASE_API}/payments/validate`,
      prefill: {
        name: user?.user?.name,
        email: user?.user?.email,
      },
      notes: {
        premiumServiceId,
      },
      theme: {
        color: "#3399cc",
      },
    };

    const rzp = new (window as any).Razorpay(options);

    rzp.on("payment.failed", function (response: any) {
      alert("Payment Failed. Please Retry again.");
      navigate("/featured/failed");
    });

    rzp.open();
  };

  const handleSuccessModal = (
    isOpen: boolean,
    state: string,
    message: string
  ) => {
    setSuccessModal(isOpen);
    setState(state);
    setMessage(message);
  };

  const handleCreate = async (value: premiumServiceCreate) => {
    try {
      if (user?.admin !== -1) {
        handleSuccessModal(true, "LOADING", "");

        console.log({ value });

        const currency = "USD";

        const { title, description, timeline, reportLength, amount } = value;

        const data = {
          title,
          description,
          timeline,
          reportLength,
          amount,
          currency,
        };

        const { order_id, premiumServiceId } = await createPremiumService(data);

        handleSuccessModal(false, "LOADING", "");

        paymentHandler(amount, currency, order_id, premiumServiceId);
      } else {
        handleLoginModal();
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className="premium-services-main">
      <Helmet>
        <title>
          {title.DISPLAYER_PREMIUM_SERVICES ||
            "GTI Services for Technology Displayers"}
        </title>
        <meta
          name="description"
          key="description"
          content={
            metaData.DISPLAYER_PREMIUM_SERVICES ||
            "GTI Market Access and Innovation Services for Technology Displayers"
          }
        />
        <meta
          name="title"
          key="title"
          content="GTI Services for Technology Displayers"
        />
        <meta
          property="og:title"
          content="GTI Services for Technology Displayers"
        />
        <meta
          property="og:description"
          content="GTI Market Access and Innovation Services for Technology Displayers"
        />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Hero Section */}
      <section className="premium-hero-section">
        <div className="premium-hero-background"></div>
        <div className="premium-hero-content">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="flex-1 text-left lg:text-left">
              <h1 className="premium-hero-title text-left">
                GTI Market Access & Innovation Services
              </h1>
              <p className="premium-hero-subtitle text-left">
                For Technology Displayers
              </p>
              <p className="text-lg text-blue-100 leading-relaxed max-w-3xl">
                Comprehensive support for technology companies, entrepreneurs,
                MSMEs, startups, and R&D institutions to deploy and validate
                their technology in new markets. We provide deep market insights
                and assistance in finding the right partnerships in your target
                market.
              </p>
            </div>
            <div className="flex-1 max-w-md">
              <div className="premium-image-container">
                <img
                  src={displayer_premium_services}
                  alt="Displayer Market Access and Innovation Services"
                  className="premium-image"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Services Section */}
      <section className="premium-services-section">
        <div className="premium-services-container">
          <h2 className="premium-section-title">
            Market Insights & Technology Validation Reports
          </h2>
          <p className="premium-section-subtitle">
            Customized reports for your technology and target market
          </p>

          <div className="premium-pricing-grid">
            {/* Market Insight Report */}
            <div className="premium-pricing-card">
              <div className="premium-pricing-header">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <DocumentTextIcon className="w-8 h-8 text-white" />
                </div>
                <h3 className="premium-pricing-title">
                  {premiumServicesDetails.marketInsightReport.title}
                </h3>
                <div className="premium-pricing-price">$1,000</div>
                <div className="premium-pricing-period">One-time fee</div>
              </div>

              <div className="premium-pricing-content">
                <p className="text-gray-600 mb-6 text-center">
                  {premiumServicesDetails.marketInsightReport.description}
                </p>

                <div className="premium-pricing-features">
                  <div className="premium-pricing-feature">
                    <ClockIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Timeline: 15 Days
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <DocumentIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Report length: 6-8 pages
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Market analysis
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Technology viability assessment
                    </span>
                  </div>
                </div>

                <button
                  className="premium-btn-primary"
                  onClick={() =>
                    handleCreate({
                      title: premiumServicesDetails.marketInsightReport.title,
                      description:
                        premiumServicesDetails.marketInsightReport.description,
                      timeline: "15",
                      reportLength: "6-8",
                      amount: 1000,
                    })
                  }
                >
                  Get Started - $1,000
                </button>
              </div>
            </div>
            {/* Detailed Market Insight Report */}
            <div className="premium-pricing-card">
              <div className="premium-pricing-header">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <ChartBarIcon className="w-8 h-8 text-white" />
                </div>
                <h3 className="premium-pricing-title">
                  {
                    premiumServicesDetails
                      .detailedMarketInsightAndTechnologyValidationeReport.title
                  }
                </h3>
                <div className="premium-pricing-price">$3,000</div>
                <div className="premium-pricing-period">One-time fee</div>
              </div>

              <div className="premium-pricing-content">
                <p className="text-gray-600 mb-6 text-center">
                  {
                    premiumServicesDetails
                      .detailedMarketInsightAndTechnologyValidationeReport
                      .description
                  }
                </p>

                <div className="premium-pricing-features">
                  <div className="premium-pricing-feature">
                    <ClockIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Timeline: 1 month
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <DocumentIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Report length: 15-20 pages
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Comprehensive market analysis
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Technology validation
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Competitor analysis
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Market entry strategy
                    </span>
                  </div>
                </div>

                <button
                  className="premium-btn-primary"
                  onClick={() =>
                    handleCreate({
                      title:
                        premiumServicesDetails
                          .detailedMarketInsightAndTechnologyValidationeReport
                          .title,
                      description:
                        premiumServicesDetails
                          .detailedMarketInsightAndTechnologyValidationeReport
                          .description,
                      timeline: "30",
                      reportLength: "15-20",
                      amount: 3000,
                    })
                  }
                >
                  Get Started - $3,000
                </button>
              </div>
            </div>
            {/* Partner/Customer/Investor Connections */}
            <div className="premium-pricing-card">
              <div className="premium-pricing-header">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <UserGroupIcon className="w-8 h-8 text-white" />
                </div>
                <h3 className="premium-pricing-title">
                  {
                    premiumServicesDetails
                      .identificationAndFacilitationOfMeetings.title
                  }
                </h3>
                <div className="premium-pricing-price">${value},000</div>
                <div className="premium-pricing-period">
                  Customizable package
                </div>
              </div>

              <div className="premium-pricing-content">
                <p className="text-gray-600 mb-6 text-center">
                  {
                    premiumServicesDetails
                      .identificationAndFacilitationOfMeetings.description
                  }
                </p>

                <div className="premium-range-container">
                  <div className="premium-range-label">
                    Number of Connections: {value}
                  </div>
                  <input
                    type="range"
                    min="5"
                    max="10"
                    step="1"
                    value={value}
                    onChange={handleChange}
                    className="premium-range-slider"
                  />
                  <div className="premium-range-values">
                    <span className="premium-range-current">{value}</span>
                    <span className="premium-range-max">10</span>
                  </div>
                  <p className="premium-range-note">
                    *Curated connections and meetings
                  </p>
                </div>

                <div className="premium-pricing-features mb-6">
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Curated partner identification
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Meeting facilitation
                    </span>
                  </div>
                  <div className="premium-pricing-feature">
                    <CheckIcon className="premium-pricing-feature-icon" />
                    <span className="premium-pricing-feature-text">
                      Follow-up support
                    </span>
                  </div>
                </div>

                {value === 5 ? (
                  <button
                    className="premium-btn-primary"
                    onClick={() =>
                      handleCreate({
                        title:
                          premiumServicesDetails
                            .identificationAndFacilitationOfMeetings.title,
                        description:
                          premiumServicesDetails
                            .identificationAndFacilitationOfMeetings
                            .description,
                        amount: value * 1000,
                      })
                    }
                  >
                    Get Started - ${value},000
                  </button>
                ) : (
                  <a
                    href="mailto:<EMAIL>"
                    className="premium-btn-email"
                  >
                    <EnvelopeIcon className="w-5 h-5" />
                    Contact Us for Custom Package
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Additional Services Section */}
      <section className="premium-services-section bg-white">
        <div className="premium-services-container">
          <h2 className="premium-section-title">
            Additional Value-Added Services
          </h2>
          <p className="premium-section-subtitle">
            Comprehensive support services to accelerate your market entry and
            technology deployment
          </p>

          <div className="premium-additional-services">
            <div className="premium-additional-grid">
              <div className="premium-additional-card">
                <div className="premium-additional-number">01</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices1.title}
                </h4>
                <p className="premium-additional-text">
                  {
                    premiumServicesDetails.additionalValueAddedServices1
                      .description
                  }
                </p>
              </div>

              <div className="premium-additional-card">
                <div className="premium-additional-number">02</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices2.title}
                </h4>
                <p className="premium-additional-text">
                  {
                    premiumServicesDetails.additionalValueAddedServices2
                      .description
                  }
                </p>
              </div>

              <div className="premium-additional-card">
                <div className="premium-additional-number">03</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices3.title}
                </h4>
                <p className="premium-additional-text">
                  {
                    premiumServicesDetails.additionalValueAddedServices3
                      .description
                  }
                </p>
              </div>

              <div className="premium-additional-card">
                <div className="premium-additional-number">04</div>
                <h4 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
                  {premiumServicesDetails.additionalValueAddedServices4.title}
                </h4>
                <p className="premium-additional-text">
                  {
                    premiumServicesDetails.additionalValueAddedServices4
                      .description
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* CTA Section */}
      <section className="premium-services-section bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white">
        <div className="premium-services-container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Accelerate Your Market Entry?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Get started with our premium services and unlock new opportunities
            for growth and market validation
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="premium-btn-email max-w-xs mx-auto sm:mx-0"
            >
              <EnvelopeIcon className="w-5 h-5" />
              Contact Us Today
            </a>
          </div>
        </div>
      </section>
      {successModal && (
        <SuccessModal
          state={state}
          message={message}
          show={successModal}
          toggle={handleSuccessModal}
        />
      )}
    </div>
  );
};

export default DisplayerPremiumServices;
