/* eslint-disable no-multi-str */
import React, { Dispatch, useEffect, useRef, useState } from "react";

import { useSelector } from "react-redux";

import Slider from "react-slick";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Helmet } from "react-helmet";
import ReactPlayer from "react-player";

import home_globe_1 from "../../assests/home/<USER>";
import home_globe_2 from "../../assests/home/<USER>";
import home_globe_3 from "../../assests/home/<USER>";
import home_globe_4 from "../../assests/home/<USER>";
import globe from "../../assests/home/<USER>";
import InteractiveGlobe from "../ui/InteractiveGlobe";
import tech_displayer from "../../assests/home/<USER>";
import tech_scouter from "../../assests/home/<USER>";
import tech_investor from "../../assests/home/<USER>";
import tech_displayer_icon from "../../assests/home/<USER>";
import tech_scouter_icon from "../../assests/home/<USER>";
import investor_icon from "../../assests/home/<USER>";
import tech_displayer_icon_dark from "../../assests/home/<USER>";
import tech_scouter_icon_dark from "../../assests/home/<USER>";
import investor_icon_dark from "../../assests/home/<USER>";
import clean_tech from "../../assests/home/<USER>";
import bio_tech from "../../assests/home/<USER>";
import digi_tech from "../../assests/home/<USER>";
import group_tech from "../../assests/home/<USER>";
import group_incub from "../../assests/home/<USER>";
import group_cluster from "../../assests/home/<USER>";
import group_ngo from "../../assests/home/<USER>";
import group_gov from "../../assests/home/<USER>";
import group_investor from "../../assests/home/<USER>";
import group_startup from "../../assests/home/<USER>";
import group_corporate from "../../assests/home/<USER>";
import tech_display from "../../assests/home/<USER>";
import market_opp from "../../assests/home/<USER>";
import innovation_call from "../../assests/home/<USER>";
import investor_connect from "../../assests/home/<USER>";
import knowledge_hub from "../../assests/home/<USER>";

import star from "../../assests/home/<USER>";
import * as C from "../constants";
import { getProducts } from "../../store/actioncreators/productactions";
import { getOpportunities } from "../../store/actioncreators/opportunityactions";
import { getCalls } from "../../store/actioncreators/innovationactions";
import NewsLetter from "../LatestUpdates/NewsLetter";
import { getQueryParams } from "../../utils";
import RenderHTML from "../utils/RenderHTML";
import AnimateCount from "../utils/AnimateCount";
import { getArticles } from "../../store/actioncreators/articleactions";
import biotechnology from "../../assests/images/biotechnology.jpg";
import cleantech from "../../assests/images/cleantech.jpg";
import informationTechnology from "../../assests/images/information-technology.jpg";
import { getNews } from "../../store/actioncreators/newsAction";
import articlebanner from "../../assests/banners/articlebanner.png";

import "./style.css";
import { getTechnologyPartners } from "../../store/actioncreators/technologypartneractions";
import { fetchHomeapgePromotion } from "../../store/actioncreators/promotionsaction";
import { updates } from "../../shared/cohort/updates";
import { client_review } from "../../shared/constants";
import supply_chain from "../../assests/images/premium-services/supply-chain.svg";

const cohortHomepageBackground = "/images/cohort/cohort_homepage_bg.svg";

// Enhanced Animated Trusted By Component
const TrustedByAnimation = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const companies = [
    { name: "ABC Green Energy", color: "from-green-400 to-emerald-500" },
    { name: "DEF Climate", color: "from-blue-400 to-cyan-500" },
    { name: "Solar Energy Limited", color: "from-yellow-400 to-orange-500" },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false);

      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % companies.length);
        setIsVisible(true);
      }, 400); // Smooth transition timing
    }, 3500); // Slightly longer display time

    return () => clearInterval(interval);
  }, [companies.length]);

  return (
    <div className="mt-6 relative">
      {/* Background glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-lg blur-sm"></div>

      {/* Main content */}
      <div className="relative bg-white/5 backdrop-blur-sm rounded-xl px-6 py-4 border border-white/10 hover:border-white/20 transition-all duration-500">
        <div className="flex items-center justify-center space-x-2">
          {/* Trust indicator icon */}
          <div className="flex-shrink-0">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          </div>

          {/* Trusted by text */}
          <p className="text-gray-200 text-sm font-medium tracking-wide">
            Trusted by{" "}
            <span
              className={`inline-block transition-all duration-500 ease-out font-semibold ${
                isVisible
                  ? "opacity-100 transform translate-y-0 scale-100"
                  : "opacity-0 transform translate-y-3 scale-95"
              }`}
            >
              <span
                className={`bg-gradient-to-r ${companies[currentIndex].color} bg-clip-text text-transparent`}
              >
                {companies[currentIndex].name}
              </span>
            </span>
          </p>

          {/* Progress indicators */}
          <div className="flex space-x-1 ml-3">
            {companies.map((_, index) => (
              <div
                key={index}
                className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-white scale-125"
                    : "bg-white/30 scale-100"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Subtle animation line */}
        <div className="mt-2 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      </div>
    </div>
  );
};

const getDefaultImage = (sectorName: string) => {
  if (sectorName === "Biotechnology") {
    return biotechnology;
  } else if (sectorName === "Clean Technology") {
    return cleantech;
  } else {
    return informationTechnology;
  }
};

const LatestTechCard = ({ item }: { item: C.productItemFullFetched }) => {
  return (
    <div className="modern-latest-card group">
      <div className="modern-latest-card-image">
        <img
          src={item.image ? item.image : getDefaultImage(item.sectors.name)}
          alt={item.company.name}
          className="w-full h-full object-cover"
        />
        <div className="modern-latest-card-overlay">
          <span className="modern-latest-card-badge">🚀</span>
        </div>
      </div>
      <div className="modern-latest-card-content">
        <p className="modern-latest-card-date">
          {new Date(item.createdAt).toLocaleDateString()}
        </p>
        <h4 className="modern-latest-card-title">{item.name}</h4>
        <div className="modern-latest-card-description">
          <RenderHTML html={item.description.substring(0, 80) + "..."} />
        </div>
      </div>
    </div>
  );
};

const LatestOppCard = ({ item }: { item: C.oppotunityItemPartialFetched }) => {
  return (
    <div className="modern-latest-card group">
      <div className="modern-latest-card-image">
        <img
          src={
            item.image
              ? item.image
              : item.company.logo
              ? item.company.logo
              : getDefaultImage(item.sectors.name)
          }
          alt={item.company.name}
          className="w-full h-full object-cover"
        />
        <div className="modern-latest-card-overlay">
          <span className="modern-latest-card-badge">💼</span>
        </div>
      </div>
      <div className="modern-latest-card-content">
        <p className="modern-latest-card-date">
          {new Date(item.createdAt).toLocaleDateString()}
        </p>
        <h4 className="modern-latest-card-title">
          {item.technologyPartnerRequirement}
        </h4>
        <div className="modern-latest-card-description">
          <RenderHTML html={item.description.substring(0, 80) + "..."} />
        </div>
      </div>
    </div>
  );
};
const LatestCallCard = ({ item }: { item: C.InnovationItemFetched }) => {
  return (
    <div className="modern-latest-card group">
      <div className="modern-latest-card-image">
        <img
          src={item.companyLogo}
          alt={item.title}
          className="w-full h-full object-cover"
        />
        <div className="modern-latest-card-overlay">
          <span className="modern-latest-card-badge">💡</span>
        </div>
      </div>
      <div className="modern-latest-card-content">
        <p className="modern-latest-card-date">
          {new Date(item.createdAt).toLocaleDateString()}
        </p>
        <h4 className="modern-latest-card-title">{item.companyName}</h4>
        <div className="modern-latest-card-description">
          <RenderHTML html={item.description.substring(0, 80) + "..."} />
        </div>
      </div>
    </div>
  );
};

const LatestArticlesCard = ({ item }: { item: C.articleItemFetched }) => {
  return (
    <div className="modern-latest-card group">
      <div className="modern-latest-card-image">
        {!item.youtubeLink || item.youtubeLink === "none" ? (
          <img
            src={item.imageUrl}
            className="w-full h-full object-cover"
            alt={item.topic}
          />
        ) : (
          <ReactPlayer
            url={item.youtubeLink === C.NONE ? articlebanner : item.youtubeLink}
            height="100%"
            width="100%"
            alt={item}
            light
          />
        )}
        <div className="modern-latest-card-overlay">
          <span className="modern-latest-card-badge">📰</span>
        </div>
      </div>
      <div className="modern-latest-card-content">
        <p className="modern-latest-card-date">
          {new Date(item.createdAt).toLocaleDateString()}
        </p>
        <h4 className="modern-latest-card-title">
          {item?.topic?.replace(/(<([^>]+)>)/gi, "")}
        </h4>
        <div className="modern-latest-card-description">
          {item.shortDescription
            ?.replace(/(<([^>]+)>)/gi, "")
            .substring(0, 80) + "..."}
        </div>
      </div>
    </div>
  );
};

const LatestNewsCard = ({ item }: { item: C.newsFetched }) => {
  return (
    <div className="modern-latest-card group">
      <div className="modern-latest-card-image">
        <img
          src={item.imageUrl}
          alt={item.title}
          className="w-full h-full object-cover"
        />
        <div className="modern-latest-card-overlay">
          <span className="modern-latest-card-badge">📢</span>
        </div>
      </div>
      <div className="modern-latest-card-content">
        <p className="modern-latest-card-date">
          {new Date(item.createdAt).toLocaleDateString()}
        </p>
        <h4 className="modern-latest-card-title">
          {item?.title?.replace(/(<([^>]+)>)/gi, "")}
        </h4>
        <div className="modern-latest-card-description">
          {item.shortDescription
            ?.replace(/(<([^>]+)>)/gi, "")
            .substring(0, 80) + "..."}
        </div>
      </div>
    </div>
  );
};

const Featured = ({ promotion }: { promotion: C.featuredItem }) => {
  const navigate = useNavigate();

  return (
    <div
      className="homepage-card cursor-pointer h-full group"
      onClick={() => {
        navigate(`/featuredview/${promotion._id}`);
      }}
    >
      <div className="homepage-card-header">
        <img
          src={promotion.images[0]}
          alt="Featured"
          className="w-full h-48 object-cover rounded-2xl group-hover:scale-105 transition-transform duration-300"
        />
      </div>
      <div className="homepage-card-content">
        <h2 className="homepage-card-title text-xl mb-4">
          {promotion.contentTitle}
        </h2>
        <div className="homepage-card-description">
          {promotion.contentDescription.length > 500 ? (
            <RenderHTML
              html={promotion.contentDescription.substring(0, 500) + "..."}
            />
          ) : (
            <RenderHTML html={promotion.description} />
          )}
        </div>
      </div>
    </div>
  );
};

const Homepage = ({
  handleLogin,
  handleWebsiteModal,
  handleSponsorModal,
}: {
  handleLogin: () => void;
  handleWebsiteModal: () => void;
  handleSponsorModal: () => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const hasTriggeredRef = useRef(false);
  const hasSponsorTriggeredRef = useRef(false);

  useEffect(() => {
    dispatch(getProducts(C.SKIP, "4"));
    dispatch(getOpportunities(C.SKIP, "4"));
    dispatch(getCalls(getQueryParams("skip") ?? "0", "4"));
    dispatch(getArticles("", C.SKIP, "0"));
    dispatch(getNews(C.SKIP, "0"));
    dispatch(getTechnologyPartners(C.SKIP, "100"));
    dispatch(fetchHomeapgePromotion());
  }, [dispatch]);

  // Auto-trigger website details modal for non-logged-in users
  useEffect(() => {
    const isLoggedIn = localStorage.getItem("GTI_data");

    // Only trigger if user is not logged in and hasn't been triggered yet
    if (!isLoggedIn && !hasTriggeredRef.current) {
      hasTriggeredRef.current = true;
      handleWebsiteModal();
    }
  }, [handleLogin, handleWebsiteModal]);

  // Scroll-triggered sponsor modal for logged-in users
  useEffect(() => {
    const handleScroll = () => {
      const isLoggedIn = localStorage.getItem("GTI_data");
      const currentScrollY = window.scrollY;

      // Only trigger for logged-in users after scrolling 500px and hasn't been triggered yet
      if (
        isLoggedIn &&
        !hasSponsorTriggeredRef.current &&
        currentScrollY > 500
      ) {
        hasSponsorTriggeredRef.current = true;
        handleSponsorModal();
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [handleSponsorModal]);

  const navigate = useNavigate();

  const products: PRODUCTS = useSelector(
    (state: STATE) => state.PRODUCTS.PRODUCTS
  );

  const opp: OPP = useSelector((state: STATE) => state.OPP.OPP);

  const innovations: INNOVATION = useSelector(
    (state: STATE) => state.INNOVATION.INNOVATION
  );

  const articlelist: ARTICLE = useSelector(
    (state: STATE) => state.ARTICLE.ARTICLE
  );

  const newsList: NEWS_STATE = useSelector((state: STATE) => state.NEWS);

  const technologyPartners: TECHNOLOGY_PARTNER_STATE = useSelector(
    (state: STATE) => state.TECHNOLOGY_PARTNER
  );

  const { homepagePromotions }: PROMOTIONS_STATE = useSelector(
    (state: STATE) => state.PROMOTIONS
  );

  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);

  const [isHovered, setIsHovered] = useState(false);
  const [clientId, setClientId] = useState(-1);

  const [platformNumbers, setPlatformNumbers] = useState<any>([]);

  useEffect(() => {
    if (
      !products?.TOTAL ||
      !opp?.TOTAL ||
      !innovations?.INNOVATION_COUNT ||
      !technologyPartners?.technologyPartner?.length
    )
      return;

    setPlatformNumbers([
      {
        count: products?.TOTAL,
        title: "Technologies",
      },
      {
        count: opp?.TOTAL,
        title: "Opportunities",
      },
      {
        count: innovations?.INNOVATION_COUNT,
        title: "Innovation Calls",
      },
      {
        count: technologyPartners?.technologyPartner?.length,
        title: "Partners",
      },
    ]);
  }, [products, opp, innovations, technologyPartners, dispatch]);

  const featured_carousal_settings = {
    dots: false,
    infinite: true,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    swipeToSlide: true,
    autoplay: true,
    autoplaySpeed: 4000,
  };

  return (
    <div className="home-container">
      <Helmet>
        <title>{C.title.HOMEPAGE}</title>
        <meta
          name="description"
          key="description"
          content={C.metaData.HOMEPAGE}
        />
        <meta name="title" key="title" content="GLOBAL TECHNOLOGY INTERFACE" />
        <meta property="og:title" content="GLOBAL TECHNOLOGY INTERFACE" />
        <meta
          property="og:description"
          content="Display, Discover, Develop and Deploy"
        />
        <meta property="og:image" content={globe} />
        <meta property="og:url" content={`${process.env.REACT_APP_BASE_URL}`} />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="GLOBAL TECHNOLOGY INTERFACE" />
        <meta
          name="twitter:description"
          content="GLOBAL TECHNOLOGY INTERFACE"
        />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content="GLOBAL TECHNOLOGY INTERFACE" />
      </Helmet>

      {/* Modern Hero Section */}
      <div className="modern-hero-section">
        <div className="modern-hero-content">
          {/* Left Side - Text Content and Button */}
          <div className="modern-hero-left">
            <div className="modern-hero-text">
              <h1 className="modern-hero-title">Global Technology Interface</h1>
              <p className="modern-hero-subtitle">
                A platform to showcase innovative technologies and market
                opportunities. Access our global network to make meaningful
                partnerships and explore significant collaboration
                opportunities, all in one place.
              </p>
            </div>

            {/* CTA Button - Only show for non-logged-in users */}
            <div className="flex flex-col gap-4 justify-start items-start">
              {currentUser.admin === -1 && (
                <button
                  className="modern-cta-button modern-cta-primary"
                  onClick={() => navigate("/signup")}
                >
                  <span className="relative z-10">Get Started</span>
                </button>
              )}

              {/* Trusted by text with animation */}
              {/* <TrustedByAnimation /> */}
            </div>
          </div>

          {/* Right Side - Visual Content */}
          <div className="modern-hero-right">
            {homepagePromotions?.length ? (
              <div className="w-full max-w-lg">
                <Slider
                  {...featured_carousal_settings}
                  className="rounded-3xl overflow-hidden shadow-2xl"
                >
                  {homepagePromotions.map((promotion) => (
                    <Featured key={promotion._id} promotion={promotion} />
                  ))}
                </Slider>
              </div>
            ) : (
              <div className="globe-ecosystem">
                {/* Central Globe */}
                <div className="globe-center">
                  <InteractiveGlobe
                    height="h-[500px]"
                    className="modern-globe-interactive"
                  />
                </div>

                {/* Floating Feature Cards */}
                <div className="globe-features-grid">
                  <div className="feature-card feature-card-1">
                    <div className="feature-icon">
                      <img
                        src={home_globe_2}
                        alt="Technology Showcase"
                        className="w-6 h-6"
                      />
                    </div>
                    <div className="feature-content">
                      <h3 className="feature-title">Technology Showcase</h3>
                      <p className="feature-description">
                        Showcase and scale technology access for UN SDGs
                      </p>
                    </div>
                  </div>

                  <div className="feature-card feature-card-2">
                    <div className="feature-icon">
                      <img
                        src={home_globe_3}
                        alt="Innovation Calls"
                        className="w-6 h-6"
                      />
                    </div>
                    <div className="feature-content">
                      <h3 className="feature-title">Innovation Calls</h3>
                      <p className="feature-description">
                        Identify innovative technologies for strategic growth
                      </p>
                    </div>
                  </div>

                  <div className="feature-card feature-card-3">
                    <div className="feature-icon">
                      <img
                        src={home_globe_1}
                        alt="Market Opportunities"
                        className="w-6 h-6"
                      />
                    </div>
                    <div className="feature-content">
                      <h3 className="feature-title">Market Opportunities</h3>
                      <p className="feature-description">
                        Discover new markets, customers, and partners
                      </p>
                    </div>
                  </div>

                  <div className="feature-card feature-card-4">
                    <div className="feature-icon">
                      <img
                        src={home_globe_4}
                        alt="Knowledge Hubs"
                        className="w-6 h-6"
                      />
                    </div>
                    <div className="feature-content">
                      <h3 className="feature-title">Knowledge Hubs</h3>
                      <p className="feature-description">
                        Access global business and innovation news
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modern Featured Profile Section */}
      <div className="modern-showcase-section">
        <div className="max-w-7xl mx-auto">
          <div className="showcase-container">
            {/* Header */}
            <div className="showcase-header">
              <div className="showcase-badge">
                <span className="badge-icon">⭐</span>
                <span className="badge-text">Featured</span>
              </div>

              <h2 className="showcase-title">Showcase Your Innovation</h2>

              <p className="showcase-subtitle">
                Join our featured innovators and get global visibility for your
                technology. Connect with investors, partners, and customers
                worldwide.
              </p>
            </div>

            {/* Benefits Grid */}
            <div className="showcase-benefits">
              <div className="benefit-card">
                <div className="benefit-icon">🌍</div>
                <h3 className="benefit-title">Global Visibility</h3>
                <p className="benefit-description">
                  Reach millions of potential partners, investors, and customers
                  across 190+ countries
                </p>
              </div>

              <div className="benefit-card">
                <div className="benefit-icon">🤝</div>
                <h3 className="benefit-title">Strategic Partnerships</h3>
                <p className="benefit-description">
                  Connect with industry leaders, government agencies, and
                  innovation hubs
                </p>
              </div>

              <div className="benefit-card">
                <div className="benefit-icon">💰</div>
                <h3 className="benefit-title">Investment Opportunities</h3>
                <p className="benefit-description">
                  Access to venture capital, angel investors, and funding
                  programs
                </p>
              </div>

              <div className="benefit-card">
                <div className="benefit-icon">📈</div>
                <h3 className="benefit-title">Market Expansion</h3>
                <p className="benefit-description">
                  Scale your technology to new markets and accelerate growth
                </p>
              </div>
            </div>

            {/* Stats Section */}
            <div className="showcase-stats">
              <div className="stat-card">
                <span className="stat-number">500+</span>
                <span className="stat-label">Featured Companies</span>
              </div>
              <div className="stat-card">
                <span className="stat-number">50M+</span>
                <span className="stat-label">Global Reach</span>
              </div>
              <div className="stat-card">
                <span className="stat-number">95%</span>
                <span className="stat-label">Success Rate</span>
              </div>
              <div className="stat-card">
                <span className="stat-number">24/7</span>
                <span className="stat-label">Platform Access</span>
              </div>
            </div>

            {/* CTA Section */}
            <div className="showcase-cta">
              <button
                className="showcase-button"
                onClick={() => navigate("/featured")}
              >
                <span className="button-text">Feature Your Profile</span>
                <span className="button-icon">🚀</span>
              </button>

              <p className="cta-note">
                Join thousands of innovators already featured on our platform
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tagline Section */}
      <div className="homepage-section homepage-bg-gradient-primary homepage-text-light">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="homepage-heading-primary mb-8 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
            Display, Discover, Develop and Deploy
          </h2>
        </div>
      </div>

      {/* Platform Statistics */}
      <div className="homepage-stats-container">
        <div className="homepage-stats-grid">
          {platformNumbers &&
            platformNumbers.map((data: any) => (
              <div key={data?.title} className="homepage-stat-item">
                <AnimateCount targetValue={data?.count} title={data?.title} />
              </div>
            ))}
        </div>
      </div>

      {/* Cohort Section */}
      <div className="homepage-section relative overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{ backgroundImage: `url(${cohortHomepageBackground})` }}
        ></div>
        <div className="relative z-10 max-w-7xl mx-auto">
          <h2 className="homepage-section-title">
            Dignified Jobs Accelerator Circular Innovation Cohort 2025
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {updates.slice(0, 4).map((update) => (
              <div
                key={update.id}
                className="homepage-card group overflow-hidden"
              >
                <div className="relative h-48">
                  <img
                    src={update.imageUrl}
                    alt={update.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent">
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      {update?.link ? (
                        <a
                          href={update?.link}
                          className="text-lg font-bold hover:text-blue-200 transition-colors"
                        >
                          {update.title}
                        </a>
                      ) : (
                        <h3 className="text-lg font-bold">{update.title}</h3>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Link
              to={C.COHORT_DETAILS}
              className="homepage-cta-button homepage-cta-primary"
            >
              Learn more
            </Link>
          </div>
        </div>
      </div>

      {/* Supply Chain Section */}
      <div className="modern-supply-chain-section">
        <div className="max-w-7xl mx-auto">
          <div className="supply-chain-container">
            {/* Header */}
            <div className="supply-chain-header">
              <div className="supply-chain-badge">
                <span className="badge-icon">🔗</span>
                <span className="badge-text">Supply Chain Solutions</span>
              </div>

              <h2 className="supply-chain-title">Localising Supply Chain</h2>

              <p className="supply-chain-subtitle">
                Transform your global supply chain strategy with our
                comprehensive localization solutions in India
              </p>
            </div>

            {/* Main Content */}
            <div className="supply-chain-content">
              <div className="supply-chain-info">
                <div className="info-card">
                  <div className="info-content">
                    <p className="info-description">
                      Global Business Inroads (GBI) enables companies to
                      localize their supply chains in India by identifying
                      reliable suppliers, ensuring regulatory compliance, and
                      optimizing procurement strategies. Our end-to-end support
                      helps businesses establish strong, cost-effective, and
                      sustainable supplier networks.
                    </p>

                    <div className="info-features">
                      <div className="feature-item">
                        <div className="feature-icon">✓</div>
                        <span className="feature-text">
                          Reliable Supplier Identification
                        </span>
                      </div>
                      <div className="feature-item">
                        <div className="feature-icon">✓</div>
                        <span className="feature-text">
                          Regulatory Compliance
                        </span>
                      </div>
                      <div className="feature-item">
                        <div className="feature-icon">✓</div>
                        <span className="feature-text">
                          Procurement Optimization
                        </span>
                      </div>
                      <div className="feature-item">
                        <div className="feature-icon">✓</div>
                        <span className="feature-text">End-to-End Support</span>
                      </div>
                    </div>

                    <div className="supply-chain-cta">
                      <a
                        href="https://www.globalbusinessinroads.com/supply-chain"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="supply-chain-button"
                      >
                        <span className="button-text">Explore Solutions</span>
                        <span className="button-icon">→</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div className="supply-chain-visual">
                <div className="visual-container">
                  <img
                    src={supply_chain}
                    alt="Supply Chain Localization"
                    className="supply-chain-image"
                  />
                  <div className="visual-glow"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Who uses GTI Section */}
      <div className="modern-users-section">
        <div className="max-w-7xl mx-auto">
          <div className="users-header">
            <div className="users-badge">
              <span className="badge-icon">👥</span>
              <span className="badge-text">Our Community</span>
            </div>

            <h2 className="users-title">Who uses GTI?</h2>

            <p className="users-subtitle">
              Join thousands of innovators, organizations, and investors who
              trust GTI to connect, collaborate, and accelerate technological
              advancement worldwide.
            </p>
          </div>

          <div className="users-grid">
            <div className="user-card">
              <div className="user-card-header">
                <div className="user-card-image-container">
                  <img
                    src={tech_displayer}
                    alt="Tech Displayer"
                    className="user-card-main-image"
                  />
                  <div className="user-card-overlay">
                    <div className="overlay-logos">
                      <img
                        src={group_tech}
                        alt="Tech Companies"
                        className="overlay-logo"
                      />
                      <img
                        src={group_incub}
                        alt="Group Incubators"
                        className="overlay-logo"
                      />
                      <img
                        src={group_cluster}
                        alt="Group Cluster"
                        className="overlay-logo"
                      />
                    </div>
                  </div>
                </div>
                <div className="user-card-icon">
                  <img
                    src={tech_displayer_icon}
                    alt="Tech Displayer Icon"
                    className="icon-default"
                  />
                  <img
                    src={tech_displayer_icon_dark}
                    alt="Tech Displayer Icon Dark"
                    className="icon-hover"
                  />
                </div>
              </div>
              <div className="user-card-content">
                <h3 className="user-card-title">Technology Displayers</h3>
                <p className="user-card-description">
                  Technology companies, incubators and accelerators looking to
                  access new markets and opportunities to deploy their products
                  and services globally.
                </p>
                <div className="user-card-stats">
                  <div className="stat-item">
                    <span className="stat-number">500+</span>
                    <span className="stat-label">Companies</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">50+</span>
                    <span className="stat-label">Countries</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="user-card">
              <div className="user-card-header">
                <div className="user-card-image-container">
                  <img
                    src={tech_scouter}
                    alt="Tech Scouter"
                    className="user-card-main-image"
                  />
                  <div className="user-card-overlay">
                    <div className="overlay-logos">
                      <img
                        src={group_ngo}
                        alt="NGOs"
                        className="overlay-logo"
                      />
                      <img
                        src={group_gov}
                        alt="Government"
                        className="overlay-logo"
                      />
                      <img
                        src={group_corporate}
                        alt="Corporates"
                        className="overlay-logo"
                      />
                    </div>
                  </div>
                </div>
                <div className="user-card-icon">
                  <img
                    src={tech_scouter_icon}
                    alt="Tech Scouter Icon"
                    className="icon-default"
                  />
                  <img
                    src={tech_scouter_icon_dark}
                    alt="Tech Scouter Icon Dark"
                    className="icon-hover"
                  />
                </div>
              </div>
              <div className="user-card-content">
                <h3 className="user-card-title">Technology Scouters</h3>
                <p className="user-card-description">
                  NGOs, Government institutions & corporates who are searching
                  for innovative solution providers to solve complex business
                  challenges and requirements.
                </p>
                <div className="user-card-stats">
                  <div className="stat-item">
                    <span className="stat-number">200+</span>
                    <span className="stat-label">Organizations</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">30+</span>
                    <span className="stat-label">Sectors</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="user-card">
              <div className="user-card-header">
                <div className="user-card-image-container">
                  <img
                    src={tech_investor}
                    alt="Tech Investor"
                    className="user-card-main-image"
                  />
                  <div className="user-card-overlay">
                    <div className="overlay-logos">
                      <img
                        src={group_investor}
                        alt="Investors"
                        className="overlay-logo"
                      />
                      <img
                        src={group_startup}
                        alt="Startups"
                        className="overlay-logo"
                      />
                    </div>
                  </div>
                </div>
                <div className="user-card-icon">
                  <img
                    src={investor_icon}
                    alt="Investor Icon"
                    className="icon-default"
                  />
                  <img
                    src={investor_icon_dark}
                    alt="Investor Icon Dark"
                    className="icon-hover"
                  />
                </div>
              </div>
              <div className="user-card-content">
                <h3 className="user-card-title">Investors</h3>
                <p className="user-card-description">
                  Entities who assist startups with capital and bring a wealth
                  of market and business knowledge to help them scale in the
                  right direction and achieve sustainable growth.
                </p>
                <div className="user-card-stats">
                  <div className="stat-item">
                    <span className="stat-number">150+</span>
                    <span className="stat-label">Investors</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">$2B+</span>
                    <span className="stat-label">Funding</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Sectors we work in */}
      <div className="modern-sectors-section">
        <div className="max-w-7xl mx-auto">
          <div className="sectors-header">
            <div className="sectors-badge">
              <span className="badge-icon">🏭</span>
              <span className="badge-text">Our Focus Areas</span>
            </div>

            <h2 className="sectors-title">Sectors we work in</h2>

            <p className="sectors-subtitle">
              We specialize in cutting-edge technologies across three key
              sectors that are shaping the future of sustainable innovation and
              global development.
            </p>
          </div>

          <div className="sectors-grid">
            <div className="sector-card">
              <div className="sector-card-header">
                <div className="sector-icon-container">
                  <img
                    src={clean_tech}
                    alt="Clean Tech"
                    className="sector-icon"
                  />
                </div>
                <div className="sector-overlay">
                  <div className="overlay-content">
                    <span className="overlay-text">🌱 Sustainable Future</span>
                  </div>
                </div>
              </div>
              <div className="sector-card-content">
                <h3 className="sector-card-title">Clean Technology</h3>
                <p className="sector-card-description">
                  Products and services that promote climate action and reduce
                  negative environmental impacts through carbon emission
                  reduction and sustainable resource utilization.
                </p>
                <div className="sector-features">
                  <span className="feature-tag">Renewable Energy</span>
                  <span className="feature-tag">Carbon Reduction</span>
                  <span className="feature-tag">Sustainability</span>
                </div>
              </div>
            </div>

            <div className="sector-card">
              <div className="sector-card-header">
                <div className="sector-icon-container">
                  <img src={bio_tech} alt="Bio Tech" className="sector-icon" />
                </div>
                <div className="sector-overlay">
                  <div className="overlay-content">
                    <span className="overlay-text">🧬 Health Innovation</span>
                  </div>
                </div>
              </div>
              <div className="sector-card-content">
                <h3 className="sector-card-title">
                  BioTechnology & HealthTech
                </h3>
                <p className="sector-card-description">
                  Advanced products and services designed to improve health
                  outcomes and revolutionize healthcare delivery through
                  innovative biotechnological solutions.
                </p>
                <div className="sector-features">
                  <span className="feature-tag">Medical Devices</span>
                  <span className="feature-tag">Pharmaceuticals</span>
                  <span className="feature-tag">Digital Health</span>
                </div>
              </div>
            </div>

            <div className="sector-card">
              <div className="sector-card-header">
                <div className="sector-icon-container">
                  <img
                    src={digi_tech}
                    alt="Digi Tech"
                    className="sector-icon"
                  />
                </div>
                <div className="sector-overlay">
                  <div className="overlay-content">
                    <span className="overlay-text">💻 Digital Future</span>
                  </div>
                </div>
              </div>
              <div className="sector-card-content">
                <h3 className="sector-card-title">
                  Digital & Information Technology
                </h3>
                <p className="sector-card-description">
                  Cutting-edge digital transformation solutions and information
                  technologies that prioritize resilience, sustainability, and
                  innovative business processes.
                </p>
                <div className="sector-features">
                  <span className="feature-tag">AI & ML</span>
                  <span className="feature-tag">IoT Solutions</span>
                  <span className="feature-tag">Cloud Tech</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Latest on GTI Section */}
      <div className="innovative-latest-section">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="latest-header-innovative">
            <div className="header-content">
              <h2 className="latest-title-innovative">Latest on GTI</h2>

              <p className="latest-subtitle-innovative">
                Discover the newest technologies, opportunities, and insights
                from our global innovation ecosystem
              </p>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="latest-dashboard-grid">
            {/* Featured Technology Spotlight */}
            <div
              className="dashboard-card featured-tech-card"
              onClick={() => navigate(C.TECHNOLOGY)}
            >
              <div className="card-header-dashboard">
                <div className="header-left">
                  <div className="category-icon tech-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                    </svg>
                  </div>
                  <div className="header-text">
                    <h3 className="dashboard-card-title">
                      Latest Technologies
                    </h3>
                    <p className="dashboard-card-subtitle">
                      Breakthrough innovations
                    </p>
                  </div>
                </div>
              </div>

              <div className="card-content-dashboard">
                <div className="content-items">
                  {products.PRODUCTS_LIST?.products
                    .slice(0, 3)
                    .map((item: C.productItemFullFetched, id) => (
                      <LatestTechCard item={item} key={id} />
                    ))}
                </div>
              </div>

              <div className="card-footer-dashboard">
                <div className="footer-stats">
                  <span className="stat-text">
                    +{products.PRODUCTS_LIST?.products?.length || 0}{" "}
                    technologies
                  </span>
                </div>
                <div className="footer-action">
                  <span className="action-text">Explore All</span>
                  <svg
                    className="action-arrow"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Market Opportunities */}
            <div
              className="dashboard-card opportunities-card"
              onClick={() => navigate(C.OPPORTUNITY)}
            >
              <div className="card-header-dashboard">
                <div className="header-left">
                  <div className="category-icon opp-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                    </svg>
                  </div>
                  <div className="header-text">
                    <h3 className="dashboard-card-title">
                      Market Opportunities
                    </h3>
                    <p className="dashboard-card-subtitle">
                      Business growth potential
                    </p>
                  </div>
                </div>
              </div>

              <div className="card-content-dashboard">
                <div className="content-items">
                  {opp.OPP_LIST?.opportunities
                    .slice(0, 3)
                    .map((item: C.oppotunityItemPartialFetched, id) => (
                      <LatestOppCard item={item} key={id} />
                    ))}
                </div>
              </div>

              <div className="card-footer-dashboard">
                <div className="footer-stats">
                  <span className="stat-text">
                    +{opp.OPP_LIST?.opportunities?.length || 0} opportunities
                  </span>
                </div>
                <div className="footer-action">
                  <span className="action-text">View Market</span>
                  <svg
                    className="action-arrow"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Innovation Calls */}
            <div
              className="dashboard-card innovation-card"
              onClick={() => navigate(C.INNOVATION)}
            >
              <div className="card-header-dashboard">
                <div className="header-left">
                  <div className="category-icon innovation-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M9 11H7v3h2v-3zm4 0h-2v3h2v-3zm4 0h-2v3h2v-3zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z" />
                    </svg>
                  </div>
                  <div className="header-text">
                    <h3 className="dashboard-card-title">Innovation Calls</h3>
                    <p className="dashboard-card-subtitle">Active challenges</p>
                  </div>
                </div>
              </div>

              <div className="card-content-dashboard">
                <div className="content-items">
                  {innovations.INNOVATION_LIST.slice(0, 3).map(
                    (item: C.InnovationItemFetched, id) => (
                      <LatestCallCard item={item} key={id} />
                    )
                  )}
                </div>
              </div>

              <div className="card-footer-dashboard">
                <div className="footer-stats">
                  <span className="stat-text">
                    {innovations.INNOVATION_LIST?.length || 0} active calls
                  </span>
                </div>
                <div className="footer-action">
                  <span className="action-text">Join Challenge</span>
                  <svg
                    className="action-arrow"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Recent News & Articles */}
            <div
              className="dashboard-card news-card"
              onClick={() => navigate(C.NEWS)}
            >
              <div className="card-header-dashboard">
                <div className="header-left">
                  <div className="category-icon news-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z" />
                    </svg>
                  </div>
                  <div className="header-text">
                    <h3 className="dashboard-card-title">Latest News</h3>
                    <p className="dashboard-card-subtitle">Industry insights</p>
                  </div>
                </div>
              </div>

              <div className="card-content-dashboard">
                <div className="content-items">
                  {newsList?.news && newsList.news.length > 0
                    ? newsList.news
                        .slice(0, 2)
                        .map((item: C.newsFetched, id) => (
                          <LatestNewsCard item={item} key={id} />
                        ))
                    : null}
                  {articlelist?.articles && articlelist.articles.length > 0
                    ? articlelist.articles
                        .slice(0, 1)
                        .map((item: C.articleItemFetched, id) => (
                          <LatestArticlesCard item={item} key={id} />
                        ))
                    : null}
                </div>
              </div>

              <div className="card-footer-dashboard">
                <div className="footer-stats">
                  <span className="stat-text">Updated daily</span>
                </div>
                <div className="footer-action">
                  <span className="action-text">Read More</span>
                  <svg
                    className="action-arrow"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* How do we make it possible */}
      <div className="homepage-section bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="max-w-7xl mx-auto">
          <h2 className="homepage-section-title">How do we make it possible</h2>
          <div className="space-y-16">
            {/* Technology Display */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <img
                  src={tech_display}
                  alt="Technology Display"
                  className="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="order-1 lg:order-2 space-y-6">
                <h3 className="text-2xl lg:text-3xl font-bold text-GTI-BLUE-default">
                  Technology Display
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Use our platform to showcase your innovative technology,
                  connect with relevant stakeholders, access market
                  opportunities and grow your business. We feature your unique
                  technologies on our social media pages, boosting visibility
                  and accelerating connections with partners and customers.
                </p>
              </div>
            </div>

            {/* Market Opportunities */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                <h3 className="text-2xl lg:text-3xl font-bold text-GTI-BLUE-default">
                  Market Opportunities
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Governments, corporates and enterprises can leverage the GTI
                  platform with the goal of scouting for innovative
                  technologies, intellectual properties (IP), technologies and
                  solutions in order to improve their existing businesses.
                </p>
              </div>
              <div>
                <img
                  src={market_opp}
                  alt="Market Opportunities"
                  className="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            {/* Innovation Calls */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <img
                  src={innovation_call}
                  alt="Innovation Calls"
                  className="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="order-1 lg:order-2 space-y-6">
                <h3 className="text-2xl lg:text-3xl font-bold text-GTI-BLUE-default">
                  Innovation Calls
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Organizations can source innovative ideas and solutions
                  globally to address specific needs and challenges.
                </p>
              </div>
            </div>

            {/* Investor Connect */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                <h3 className="text-2xl lg:text-3xl font-bold text-GTI-BLUE-default">
                  Investor Connect
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed">
                  We support startups in connecting with investors or potential
                  partners who can help in growth stage company funding and
                  allow them to reach new heights.
                </p>
              </div>
              <div>
                <img
                  src={investor_connect}
                  alt="Investor Connect"
                  className="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            {/* Knowledge Hub */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <img
                  src={knowledge_hub}
                  alt="Knowledge Hub"
                  className="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="order-1 lg:order-2 space-y-6">
                <h3 className="text-2xl lg:text-3xl font-bold text-GTI-BLUE-default">
                  Knowledge Hub
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed">
                  Get access to our latest publications, articles and
                  newsletters and keep updated on ongoing opportunities and
                  events globally.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Client Testimonials */}
      <div className="homepage-section bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden">
        <div className="max-w-7xl mx-auto relative z-10">
          <h2 className="homepage-section-title">
            What our clients say about us
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {client_review.slice(0, 6).map((item, id) => (
              <div
                key={id}
                className="homepage-card group"
                onMouseEnter={() => {
                  setIsHovered(true);
                  setClientId(id);
                }}
                onMouseLeave={() => {
                  setIsHovered(false);
                  setClientId(-1);
                }}
              >
                <div className="homepage-card-content">
                  <div className="mb-6">
                    {isHovered && clientId === id ? (
                      <p className="text-gray-700 leading-relaxed">
                        {item.comment}
                      </p>
                    ) : (
                      <p className="text-gray-700 leading-relaxed">
                        {item.comment.length < 200
                          ? item.comment
                          : item.comment.slice(0, 200).concat("...")}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-4">
                    <img
                      src={item.img}
                      alt="Client Profile"
                      className="w-16 h-16 rounded-full object-cover border-2 border-GTI-BLUE-default/20"
                    />
                    <div>
                      <p className="font-semibold text-GTI-BLUE-default">
                        {item.name}
                      </p>
                      <p className="text-sm text-gray-500">{item.about}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Decorative elements */}
        <img
          src={star}
          alt="Star"
          className="absolute top-10 left-10 w-16 h-16 opacity-20 animate-pulse"
        />
        <img
          src={star}
          alt="Star"
          className="absolute bottom-10 right-10 w-16 h-16 opacity-20 animate-pulse"
        />
      </div>
      {/* Partners & Clients */}
      <div className="homepage-section bg-white">
        <div className="max-w-7xl mx-auto">
          <h2 className="homepage-section-title">Partners & Clients</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
            {technologyPartners.technologyPartner
              .slice(0, 12)
              .map((partner) => (
                <div
                  key={partner._id}
                  className="homepage-card p-6 flex items-center justify-center group"
                >
                  <img
                    className="w-full h-24 object-contain group-hover:scale-110 transition-transform duration-300 filter grayscale group-hover:grayscale-0"
                    src={partner.image}
                    alt={partner.name}
                  />
                </div>
              ))}
          </div>
          {technologyPartners.technologyPartner.length > 12 && (
            <div className="text-center mt-12">
              <button
                className="homepage-cta-button homepage-cta-primary"
                onClick={() => navigate("/partners")}
              >
                View All Partners
              </button>
            </div>
          )}
        </div>
      </div>
      <NewsLetter />
    </div>
  );
};

export default Homepage;
