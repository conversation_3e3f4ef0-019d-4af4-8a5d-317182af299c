import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";
import { AiOutlineClose } from "react-icons/ai";
import { YOUR_TECHNOLOGY } from "../constants";
import CustomEditor from "../shared/CustomEditor";

const CreateTechnology: React.FC = () => {
  const navigate = useNavigate();
  const [description, setDescription] = useState<string>("");
  const [formData, setFormData] = useState({
    name: "",
    sector: "",
    subSector: "",
    developmentStage: "",
    iprStatus: [] as string[],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<File[]>([]);
  const [uploadedVideos, setUploadedVideos] = useState<File[]>([]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("Form data:", {
      ...formData,
      description,
      images: uploadedImages,
      documents: uploadedDocuments,
      videos: uploadedVideos,
    });
    alert("Technology created successfully!");
    setIsSubmitting(false);
    navigate(YOUR_TECHNOLOGY);
  };

  const handleDescriptionChange = (content: string) => {
    setDescription(content);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleIprChange = (status: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      iprStatus: checked
        ? [...prev.iprStatus, status]
        : prev.iprStatus.filter((s) => s !== status),
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setUploadedImages((prev) => [...prev, ...files]);
  };

  const handleDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setUploadedDocuments((prev) => [...prev, ...files]);
  };

  const handleVideoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setUploadedVideos((prev) => [...prev, ...files]);
  };

  const removeImage = (index: number) => {
    setUploadedImages((prev) => prev.filter((_, i) => i !== index));
  };

  const removeDocument = (index: number) => {
    setUploadedDocuments((prev) => prev.filter((_, i) => i !== index));
  };

  const removeVideo = (index: number) => {
    setUploadedVideos((prev) => prev.filter((_, i) => i !== index));
  };

  const downloadDocument = (file: File) => {
    const url = URL.createObjectURL(file);
    const a = document.createElement("a");
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/20 to-indigo-50/30 w-full">
      <Helmet>
        <title>Create Technology | GTI</title>
        <meta
          name="description"
          content="Create and submit your innovative technology for review and approval"
        />
      </Helmet>

      {/* Header Section */}
      <div className="bg-white shadow-sm border-b border-gray-100 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Create New Technology
              </h1>
              <p className="text-gray-600">
                Submit your innovative technology for review and approval by our
                admin team.
              </p>
            </div>
            <button
              onClick={() => navigate(YOUR_TECHNOLOGY)}
              className="inline-flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 font-medium rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <AiOutlineClose className="w-5 h-5" />
              Cancel
            </button>
          </div>
        </div>
      </div>

      {/* Form Section */}
      <div className="w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <form onSubmit={handleSubmit} className="space-y-10">
              {/* Basic Information Section */}
              <div className="space-y-8">
                <div className="pb-4">
                  <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                    Basic Information
                  </h2>
                  <p className="text-gray-600">
                    Provide the essential details about your technology
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Technology Name */}
                  <div className="lg:col-span-2 space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Technology Name *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        required
                        value={formData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                        placeholder="Enter your technology name"
                      />
                      {formData.name && (
                        <div className="absolute right-3 top-3 text-green-500">
                          <svg
                            className="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Description */}
                  <div className="lg:col-span-2 space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Description *
                    </label>
                    <div className="bg-gray-50 p-1 rounded-xl">
                      <CustomEditor
                        onChange={handleDescriptionChange}
                        initialContent=""
                        toolbarFeatures={[
                          "bold",
                          "italic",
                          "underline",
                          "strikethrough",
                          "fontSize",
                          "textColor",
                          "orderedList",
                          "unorderedList",
                          "alignLeft",
                          "alignCenter",
                          "alignRight",
                          "alignJustify",
                          "link",
                          "image",
                          "undo",
                          "redo",
                          "codeBlock",
                          "quote",
                        ]}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Sector and Category Section */}
              <div className="space-y-8">
                <div className="pb-4">
                  <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                    Category & Classification
                  </h2>
                  <p className="text-gray-600">
                    Categorize your technology for better discoverability
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Sector */}
                  <div className="space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Sector *
                    </label>
                    <div className="relative">
                      <select
                        required
                        value={formData.sector}
                        onChange={(e) =>
                          handleInputChange("sector", e.target.value)
                        }
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 appearance-none bg-white"
                      >
                        <option value="">Select Sector</option>
                        <option value="technology">Technology</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="finance">Finance</option>
                        <option value="manufacturing">Manufacturing</option>
                        <option value="agriculture">Agriculture</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg
                          className="w-5 h-5 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                      {formData.sector && (
                        <div className="absolute right-8 top-3 text-green-500">
                          <svg
                            className="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Sub Sector */}
                  <div className="space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Sub Sector
                    </label>
                    <div className="relative">
                      <select
                        value={formData.subSector}
                        onChange={(e) =>
                          handleInputChange("subSector", e.target.value)
                        }
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 appearance-none bg-white"
                      >
                        <option value="">Select Sub Sector</option>
                        <option value="ai">Artificial Intelligence</option>
                        <option value="blockchain">Blockchain</option>
                        <option value="iot">Internet of Things</option>
                        <option value="robotics">Robotics</option>
                        <option value="biotech">Biotechnology</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg
                          className="w-5 h-5 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                      {formData.subSector && (
                        <div className="absolute right-8 top-3 text-green-500">
                          <svg
                            className="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Development Stage */}
                  <div className="space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Development Stage
                    </label>
                    <div className="relative">
                      <select
                        value={formData.developmentStage}
                        onChange={(e) =>
                          handleInputChange("developmentStage", e.target.value)
                        }
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 appearance-none bg-white"
                      >
                        <option value="">Select Development Stage</option>
                        <option value="concept">Concept</option>
                        <option value="prototype">Prototype</option>
                        <option value="testing">Testing</option>
                        <option value="pilot">Pilot</option>
                        <option value="production">Production Ready</option>
                        <option value="commercialized">Commercialized</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg
                          className="w-5 h-5 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                      {formData.developmentStage && (
                        <div className="absolute right-8 top-3 text-green-500">
                          <svg
                            className="w-5 h-5"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* IPR Status Section */}
              <div className="space-y-8">
                <div className="pb-4">
                  <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                    Intellectual Property Rights (IPR) Status
                  </h2>
                  <p className="text-gray-600">
                    Select all applicable intellectual property protections
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  {[
                    "Patent Filed",
                    "Patent Granted",
                    "Trademark Filed",
                    "Trademark Granted",
                    "Copyright Filed",
                    "Copyright Granted",
                    "Trade Secret",
                    "No IP Protection",
                  ].map((status, index) => {
                    const isSelected = formData.iprStatus.includes(status);
                    return (
                      <label
                        key={index}
                        className={`flex items-center space-x-3 p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 group ${
                          isSelected
                            ? "border-blue-500 bg-blue-50 text-blue-700"
                            : "border-gray-200 hover:border-blue-300 hover:bg-blue-50"
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) =>
                            handleIprChange(status, e.target.checked)
                          }
                          className="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <span
                          className={`text-sm font-medium transition-colors duration-200 ${
                            isSelected
                              ? "text-blue-700"
                              : "text-gray-700 group-hover:text-blue-700"
                          }`}
                        >
                          {status}
                        </span>
                        {isSelected && (
                          <div className="ml-auto text-blue-500">
                            <svg
                              className="w-4 h-4"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        )}
                      </label>
                    );
                  })}
                </div>
              </div>

              {/* File Uploads Section */}
              <div className="space-y-8">
                <div className="pb-4">
                  <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                    Media & Documents
                  </h2>
                  <p className="text-gray-600">
                    Upload supporting materials to showcase your technology
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Images */}
                  <div className="space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Images * ({uploadedImages.length} uploaded)
                    </label>

                    {/* Upload Area */}
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                      <input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={handleImageUpload}
                        className="hidden"
                        id="images"
                      />
                      <label htmlFor="images" className="cursor-pointer">
                        <div className="text-gray-500 group-hover:text-blue-600">
                          <svg
                            className="mx-auto h-16 w-16 mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <p className="text-base font-medium mb-1">
                            Click to upload images
                          </p>
                          <p className="text-sm text-gray-400">
                            PNG, JPG up to 10MB each
                          </p>
                        </div>
                      </label>
                    </div>

                    {/* Image Previews */}
                    {uploadedImages.length > 0 && (
                      <div className="grid grid-cols-2 gap-4 mt-4">
                        {uploadedImages.map((file, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Preview ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center">
                              <button
                                type="button"
                                onClick={() => removeImage(index)}
                                className="opacity-0 group-hover:opacity-100 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all duration-200"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                            <p className="text-xs text-gray-500 mt-1 truncate">
                              {file.name}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Documents */}
                  <div className="space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Documents ({uploadedDocuments.length} uploaded)
                    </label>

                    {/* Upload Area */}
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx"
                        multiple
                        onChange={handleDocumentUpload}
                        className="hidden"
                        id="documents"
                      />
                      <label htmlFor="documents" className="cursor-pointer">
                        <div className="text-gray-500 group-hover:text-blue-600">
                          <svg
                            className="mx-auto h-16 w-16 mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <p className="text-base font-medium mb-1">
                            Click to upload documents
                          </p>
                          <p className="text-sm text-gray-400">
                            PDF, DOC up to 10MB each
                          </p>
                        </div>
                      </label>
                    </div>

                    {/* Document List */}
                    {uploadedDocuments.length > 0 && (
                      <div className="space-y-2 mt-4">
                        {uploadedDocuments.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                          >
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                <svg
                                  className="w-8 h-8 text-red-500"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                  />
                                </svg>
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {file.name}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <button
                                type="button"
                                onClick={() => downloadDocument(file)}
                                className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-colors duration-200"
                                title="Download"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                  />
                                </svg>
                              </button>
                              <button
                                type="button"
                                onClick={() => removeDocument(index)}
                                className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-full transition-colors duration-200"
                                title="Remove"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Videos */}
                  <div className="space-y-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      Videos ({uploadedVideos.length} uploaded)
                    </label>

                    {/* Upload Area */}
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                      <input
                        type="file"
                        accept=".mp4,.webm,.mov,.avi"
                        multiple
                        onChange={handleVideoUpload}
                        className="hidden"
                        id="videos"
                      />
                      <label htmlFor="videos" className="cursor-pointer">
                        <div className="text-gray-500 group-hover:text-blue-600">
                          <svg
                            className="mx-auto h-16 w-16 mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                          <p className="text-base font-medium mb-1">
                            Click to upload videos
                          </p>
                          <p className="text-sm text-gray-400">
                            MP4, WEBM, MOV up to 50MB each
                          </p>
                        </div>
                      </label>
                    </div>

                    {/* Video Previews */}
                    {uploadedVideos.length > 0 && (
                      <div className="space-y-4 mt-4">
                        {uploadedVideos.map((file, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                              <video
                                src={URL.createObjectURL(file)}
                                controls
                                className="w-full h-full object-cover"
                                preload="metadata"
                              >
                                Your browser does not support the video tag.
                              </video>
                            </div>
                            <div className="absolute top-2 right-2">
                              <button
                                type="button"
                                onClick={() => removeVideo(index)}
                                className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all duration-200 opacity-80 hover:opacity-100"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                            <div className="mt-2 flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {file.name}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Progress Indicator */}
              <div className="bg-gray-50 rounded-xl p-6 mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Form Progress
                  </h3>
                  <span className="text-sm text-gray-600">
                    {Math.round(
                      (((formData.name ? 1 : 0) +
                        (description ? 1 : 0) +
                        (formData.sector ? 1 : 0) +
                        (formData.iprStatus.length > 0 ? 1 : 0)) /
                        4) *
                        100
                    )}
                    % Complete
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        (((formData.name ? 1 : 0) +
                          (description ? 1 : 0) +
                          (formData.sector ? 1 : 0) +
                          (formData.iprStatus.length > 0 ? 1 : 0)) /
                          4) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                  <div
                    className={`flex items-center space-x-2 ${
                      formData.name ? "text-green-600" : "text-gray-400"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm">Basic Info</span>
                  </div>
                  <div
                    className={`flex items-center space-x-2 ${
                      description ? "text-green-600" : "text-gray-400"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm">Description</span>
                  </div>
                  <div
                    className={`flex items-center space-x-2 ${
                      formData.sector ? "text-green-600" : "text-gray-400"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm">Category</span>
                  </div>
                  <div
                    className={`flex items-center space-x-2 ${
                      formData.iprStatus.length > 0
                        ? "text-green-600"
                        : "text-gray-400"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm">IPR Status</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  * Required fields must be completed before submission
                </div>
                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={() => navigate(YOUR_TECHNOLOGY)}
                    disabled={isSubmitting}
                    className="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={
                      isSubmitting ||
                      !formData.name ||
                      !description ||
                      !formData.sector
                    }
                    className="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-2"
                  >
                    {isSubmitting ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        <span>Creating...</span>
                      </>
                    ) : (
                      <span>Create Technology</span>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateTechnology;
