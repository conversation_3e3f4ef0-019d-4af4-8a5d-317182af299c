import { Dispatch, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Helmet } from "react-helmet";

import {
  CREATE_TECHNOLOGY,
  LIMIT,
  SKIP,
  TECHNOLOGY,
  title,
  metaData,
  sectorItem,
  subsectorItem,
} from "../constants";
import { getSector } from "../../store/actioncreators/sectoractions";
import { getSubSectorBySector } from "../../store/actioncreators/sub-sectoractions";
import ProductList from "./ProductList";
import { getQueryParams } from "../../utils";
import globe from "../../assests/home/<USER>";
import "./style.css";

// Modern icons for enhanced UI
const SearchIcon = () => (
  <svg
    className="w-5 h-5 text-gray-400"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
);

const FilterIcon = () => (
  <svg
    className="w-5 h-5 text-gray-500"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
    />
  </svg>
);

const Products = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const navigate = useNavigate();
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );
  const dispatch: Dispatch<any> = useDispatch();

  const [page, setPage] = useState({
    skip: getQueryParams("skip") ? getQueryParams("skip") : SKIP,
    limit: LIMIT,
  });
  const [maxSkip, setMaxSkip] = useState(0);

  // Sector and subsector state
  const [selectedSector, setSelectedSector] = useState({
    id: "",
    name: "All Sectors",
  });
  const [selectedSubSector, setSelectedSubSector] = useState({
    id: "",
    name: "All Sub-Sectors",
  });

  const products: PRODUCTS = useSelector(
    (state: STATE) => state.PRODUCTS.PRODUCTS
  );

  const fetchData = (value: number) => {
    let final =
      parseInt(page.skip) + value < 0
        ? parseInt(page.skip)
        : parseInt(page.skip) + value;
    setPage({ skip: final.toString(), limit: page.limit });
    navigate(TECHNOLOGY + `?skip=${final}`);
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    setMaxSkip(
      Math.ceil(products.PRODUCTS_LIST.productsCount / parseInt(LIMIT))
    );
  }, [page, products]);

  useEffect(() => {
    dispatch(getSector());
  }, [dispatch]);

  // Search state
  const [search, setSearch] = useState("");

  const handleSearch = (searchValue: string) => {
    setPage({ skip: "0", limit: LIMIT });
    setSearch(searchValue);
  };

  const handleSectorChange = (sectorId: string, sectorName: string) => {
    setSelectedSector({ id: sectorId, name: sectorName });
    setSelectedSubSector({ id: "", name: "All Sub-Sectors" });
    setPage({ skip: "0", limit: LIMIT });

    // Fetch subsectors for the selected sector
    if (sectorId) {
      dispatch(getSubSectorBySector(sectorId));
    }
  };

  const handleSubSectorChange = (
    subSectorId: string,
    subSectorName: string
  ) => {
    setSelectedSubSector({ id: subSectorId, name: subSectorName });
    setPage({ skip: "0", limit: LIMIT });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Helmet>
        <title>{title.TECHNOLOGY}</title>
        <meta
          name="description"
          key="description"
          content={metaData.TECHNOLOGY}
        />
        <meta name="title" key="title" content={title?.TECHNOLOGY} />
        <meta property="og:title" content={title.TECHNOLOGY} />
        <meta property="og:description" content={metaData.TECHNOLOGY} />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/technology`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={title?.TECHNOLOGY} />
        <meta name="twitter:description" content={metaData.TECHNOLOGY} />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content={title?.TECHNOLOGY} />
      </Helmet>

      {/* Enhanced Hero Header Section */}
      <div className="relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800">
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}
          ></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
          <div className="text-center">
            {/* Animated Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-6 animate-fadeInUp">
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
              </svg>
              Technology Showcase
            </div>

            {/* Main Title with Gradient */}
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 animate-fadeInUp delay-200">
              <span className="bg-gradient-to-r from-white via-blue-100 to-indigo-100 bg-clip-text text-transparent">
                Discover Technologies
              </span>
            </h1>

            {/* Enhanced Description */}
            <p className="text-xl sm:text-2xl text-blue-100 max-w-4xl mx-auto mb-8 leading-relaxed animate-fadeInUp delay-300">
              Explore innovative technologies from around the world and connect
              with groundbreaking solutions in our global technology ecosystem.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fadeInUp delay-500">
              <button
                onClick={() => navigate(CREATE_TECHNOLOGY)}
                className="group relative px-8 py-4 bg-white text-blue-600 font-semibold rounded-2xl hover:bg-blue-50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-2xl"
              >
                <span className="flex items-center gap-2">
                  <svg
                    className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create New Technology
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="relative bg-white/10 backdrop-blur-sm border-t border-white/20">
          <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex justify-center items-center space-x-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-white">
                  {products.PRODUCTS_LIST?.productsCount || 0}
                </div>
                <div className="text-sm text-blue-100">Technologies</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">
                  {sectorlist?.SECTOR_LIST?.length || 0}
                </div>
                <div className="text-sm text-blue-100">Sectors</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Simplified Filters and Search Section */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
          {/* Filter Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FilterIcon />
                <span className="text-lg font-semibold text-gray-900">
                  Filters & Search
                </span>
              </div>

              {/* Results Count */}
              <div className="text-sm text-gray-600">
                {products.PRODUCTS_LIST?.productsCount || 0} technologies found
              </div>
            </div>
          </div>

          {/* Quick Filters and Search */}
          <div className="space-y-4">
            {/* Sector Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Sectors
              </label>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleSectorChange("", "All Sectors")}
                  className={`px-4 py-2 text-sm rounded-full transition-colors ${
                    selectedSector.id === ""
                      ? "bg-GTI-BLUE-default text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-GTI-BLUE-default hover:text-white"
                  }`}
                >
                  All Sectors
                </button>
                {sectorlist.SECTOR_LIST.map((sector: sectorItem) => (
                  <button
                    key={sector._id}
                    onClick={() => handleSectorChange(sector._id, sector.name)}
                    className={`px-4 py-2 text-sm rounded-full transition-colors ${
                      selectedSector.id === sector._id
                        ? "bg-GTI-BLUE-default text-white"
                        : "bg-gray-100 text-gray-700 hover:bg-GTI-BLUE-default hover:text-white"
                    }`}
                  >
                    {sector.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Sub-Sector Filters - Only show when a sector is selected */}
            {selectedSector.id && subsectorlist.SUB_SECTOR_LIST.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Sub-Sectors
                </label>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleSubSectorChange("", "All Sub-Sectors")}
                    className={`px-4 py-2 text-sm rounded-full transition-colors ${
                      selectedSubSector.id === ""
                        ? "bg-GTI-BLUE-default text-white"
                        : "bg-gray-100 text-gray-700 hover:bg-GTI-BLUE-default hover:text-white"
                    }`}
                  >
                    All Sub-Sectors
                  </button>
                  {subsectorlist.SUB_SECTOR_LIST.map(
                    (subsector: subsectorItem) => (
                      <button
                        key={subsector._id}
                        onClick={() =>
                          handleSubSectorChange(subsector._id, subsector.name)
                        }
                        className={`px-4 py-2 text-sm rounded-full transition-colors ${
                          selectedSubSector.id === subsector._id
                            ? "bg-GTI-BLUE-default text-white"
                            : "bg-gray-100 text-gray-700 hover:bg-GTI-BLUE-default hover:text-white"
                        }`}
                      >
                        {subsector.name}
                      </button>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Search Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Search
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <SearchIcon />
                </div>
                <input
                  type="text"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default transition-colors"
                  placeholder="Search technologies, companies, or keywords..."
                  value={search}
                  onChange={(e) => handleSearch(e.target.value)}
                />
                {search && (
                  <button
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => handleSearch("")}
                  >
                    <svg
                      className="w-4 h-4 text-gray-400 hover:text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product List Section */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8">
        <ProductList
          skip={page.skip}
          limit={LIMIT}
          secId={selectedSector.id}
          subSecId={selectedSubSector.id}
          search={search}
          sortBy="newest"
          developmentStageFilter=""
          dateRange={{ from: "", to: "" }}
        />
      </div>

      {/* Modern Pagination */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              disabled={page.skip === "0"}
              onClick={() => fetchData(-1)}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              disabled={
                (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                products.TOTAL
              }
              onClick={() => fetchData(1)}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>

          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{" "}
                <span className="font-medium">
                  {parseInt(page.skip) * parseInt(page.limit) + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(
                    (parseInt(page.skip) + 1) * parseInt(page.limit),
                    products.TOTAL
                  )}
                </span>{" "}
                of <span className="font-medium">{products.TOTAL}</span> results
              </p>
            </div>

            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  disabled={page.skip === "0"}
                  onClick={() => fetchData(-1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                {/* Page Numbers */}
                {Array.from({ length: Math.min(5, maxSkip) }, (_, i) => {
                  const pageNum = Math.max(1, parseInt(page.skip) - 2) + i;
                  if (pageNum > maxSkip) return null;

                  return (
                    <button
                      key={pageNum}
                      onClick={() =>
                        fetchData(pageNum - parseInt(page.skip) - 1)
                      }
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === parseInt(page.skip) + 1
                          ? "z-10 bg-GTI-BLUE-default border-GTI-BLUE-default text-white"
                          : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  disabled={
                    (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                    products.TOTAL
                  }
                  onClick={() => fetchData(1)}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;
