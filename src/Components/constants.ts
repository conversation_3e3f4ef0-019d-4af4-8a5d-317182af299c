export const HOME = "/";
export const SIGNUP = "/signup";
export const TECHNOLOGY = "/technology";
export const YOUR_TECHNOLOGY = "/your-technology";
export const COMPANY_TECHNOLOGY = "/company-technology";
export const OPPORTUNITY = "/opportunities";
export const YOUR_OPPORTUNITY = "/your-opportunities";
export const COMPANY_OPPORTUNITY = "/company-opportunities";
export const INNOVATION = "/innovation";
export const HELPDESK = "/helpdesk";
export const PROFILES = "/profiles";
export const CONTACT_REQUEST = "/contact-us";
export const ONGOING = "/ongoing-connections";
export const SUBSCRIBED_USERS = "/subscribed-users";
export const TECH_PARTNERS = "/technology-partners";
export const ARTICLES = "/articles";
export const ARTICLE = "/articles/:id";
export const BLOGS = "/blogs";
export const BLOG = "/blogs/:id";
export const PUBLICATIONS = "/publications";
export const PUBLICATION = "/publications/:id";
export const NEWS = "/news";
export const NEWS_ID = "/news/:id";
export const EVENTS = "/events";
export const EVENT = "/events/:id";
export const UPDATE_PROFILES = "/updateprofiles";
export const USER_PROFILE = "/user";
export const INNOVATION_CALL = "/innovations-call";
export const INNOVATION_CALL_ID = "/innovation-call/:id";
export const INNOVATION_CALL_ID_VIEW = "/innovation-call-view/:id";
export const PRODUCT = "/product/:id";
export const YOUR_PRODUCT = "/your-product";
export const COMPANY_PRODUCT = "/company-product";
export const CREATE_TECHNOLOGY = "/create-technology";
export const CREATE_OPPORTUNITY = "/create-opportunity";
export const EDIT_TECHNOLOGY = "/edit-technology/:id";
export const EDIT_OPPORTUNITY = "/edit-opportunity/:id";
export const OPP = "/opportunity/:id";
export const YOUR_OPP = "/your-opportunity";
export const COMPANY_OPP = "/company-opportunity";
export const ABOUT = "/about";
export const NOTIFICATIONS = "/notifications";
export const CHAT_ID = "/chat/:id";
export const CHAT = "/chat";
export const CONTACTUS = "/contact-us";
export const CONNECTIONS = "/connections";
export const EMAIL_VERIFY = "/emailverify";
export const EMAIL_INPUT = "/recover/email";
export const FORGOT_PASSWORD = "/forgetpassword";
export const VIEW_PROFILE = "/view-profile/:id";
export const TERMS = "/termsconditioins";
export const PRIVACY = "/privacy-policy";
export const PROFILE = "/profile";
export const PERSONAL = "personal";
export const COMPANY = "company";
export const PASSWORD = "password";
export const NETWORK = "network";
export const PAYMENT = "payment";
export const PROMOTIONS = "/featured";
export const PROMOTIONS_LIST = "your-featured";
export const PROMOTION = "/featured/:id";
export const PROMOTION_VIEW = "/featuredview/:id";
export const REFUND_AND_CANCELLATION_POLICY = "refund-policy";
export const PROMOTION_PAYMENT_SUCCESS = "/featured/success";
export const PROMOTION_PAYMENT_FAILURE = "/featured/failed";
export const EVENT_PAYMENT_SUCCESS = "/event/success";
export const EVENT_PAYMENT_FAILURE = "/event/failed";
export const EVENT_VIDEO = "/eventvideo/:id";
export const PREMIUM_SERVICES = "/premium-services";
export const DISAPLYER_PREMIUM_SERVICES = "/displayer-services";
export const SCOUTER_PREMIUM_SERVICES = "/scouter-services";
export const SUPPLY_CHAIN = "/supply-chain";
export const MASTERCLASS = "/masterclass";
export const MASTERCLASS_ID = "/masterclass/:id";
export const YOUR_MASTERCLASS = "/your-masterclass";
export const CREATE_MASTERCLASS = "/create-masterclass";
export const MASTERCLASS_VIDEO = "/masterclass-video/:id";

export const COHORT_COMPANIES = "/selected-companies";
export const COHORT_DETAILS =
  "/dignified-jobs-accelerator-circular-innovation-cohort-2025";
export const PRICING = "/pricing";

export const CONTENT_TYPE: string = "image/png";
export const CONTENT_TYPE_DOC: string = "image/png";
export const NONE: string = "";

export const LIMIT: string = "9";
export const SKIP: string = "0";
export const APPROVED: string = "APPROVED";
export const PENDING: string = "PENDING";
export const REJECTED: string = "REJECTED";
export const UNAPPROVED: string = "UNAPPROVED";

export const HEADER_TECHNOLOGIES = "Technologies";
export const HEADER_YOUR_TECHNOLOGIES = "My Products";
export const HEADER_OPPORTUNITES = "Market Opportunites";
export const YOUR_HEADER_OPPORTUNITES = "My Opportunites";
export const HEADER_INNOVATIONCALL = "Innovation Calls";
export const HEADER_HELPDESK = "Helpdesk";
export const HEADER_ARTICLES = "Articles";
export const HEADER_BLOGS = "Blogs";
export const HEADER_PUBLICATIONS = "Publications";
export const HEADER_NEWS = "News";
export const HEADER_EVENTS = "Events";
export const HEADER_ABOUT_GTI = "About GTI®";
export const HEADER_LEARNMORE = "Learn More";
export const HEADER_CONTACT = "Contact us";
export const HEADER_PREMIUM_SERVICES_DISPLAYER = "For Displayers";
export const HEADER_PREMIUM_SERVICES_SCOUTER = "For Scouters";
export const HEADER_SUPPLY_CHAIN = "Supply Chain";
export const HEADER_MASTERCLASS = "Masterclass";
export const HEADER_PRICING = "Pricing";

export enum FILE_TYPE {
  PNG = "png",
  PDF = "pdf",
}

export enum DOCUMENT_TYPE {
  OPEN = "Open Documents",
  NDA = "Nda Documents",
}

export enum FILE_PATH {
  COMPANY_LOGO = "companies_logos",
  COMPANY_DOCS = "company_docs",
  BANNER = "banners",
  ARTICLE_IMAGE = "articles_image",
  PRODUCTS_IMAGE = "products_image",
  PRODUCTS_VIDEO = "products_video",
  PROMOTIONS_IMAGE = "promotions_image",
}

export const ARTICLE_TYPE_ARTICLE = "ARTICLE";
export const ARTICLE_TYPE_NEWS = "NEWS";
export enum ARTICLE_TYPE_ENUM {
  ARTICLE = "ARTICLE",
  NEWS = "NEWS",
  VIDEO = "VIDEO",
}
export const BLOGS_TYPE_ARTICLE = "ARTICLE";
export const BLOGS_TYPE_NEWS = "NEWS";
export enum BLOGS_TYPE_ENUM {
  ARTICLE = "ARTICLE",
  NEWS = "NEWS",
  VIDEO = "VIDEO",
}
export enum EVENTS_TYPE_ENUM {
  ONLINE = "ONLINE",
  OFFLINE = "OFFLINE",
}

export const HOME_CONTENT = {
  GTI: "GLOBAL TECH INTERFACE®",
};

export const CONTACT_NUMBER =
  "+************, +************ (between 10 AM to 6 PM IST)";
export const CONTACT_EMAIL = "<EMAIL>";
export const CONTACT_ADDRESS =
  "No:545, Trinity Greens – G-03, Ground Floor, Koramangala 4th Block, Bangalore – 560034, Karnataka, India.";

export const INNOVATION_PARTIAL_CONTENT_TOP =
  "The Global Technology Interface ® (GTI ®) can be leveraged by governments, corporates and enterprises for posting Innovation Calls, with the goal of scouting for innovation, intellectual property (IP), technologies and/or solutions externally in order to improve their existing businesses or productivity. ";
export const INNOVATION_PARTIAL_CONTENT_BOTTOM =
  "GTI ® can facilitate the identification and shortlisting of innovative startups, companies, IP in the cleantech, biotech and ICT sectors to address the specific need of clients that can bring complementary technology solutions in their areas of interest and support their strategic growth. GTI can perform technology and innovation management for businesses by providing a platform designed to address their technology management and innovation needs.";

export const INNOVATION_FULL_CONTENT =
  'Steps: 1) Develop a customized scope and objective of the "Innovation Call" along with the client, including: a) Describing the benefits for applicants. b) Defining eligibility criteria for applicants (clear value proposition, size, stage, amount of funding raised, etc.). c) Designing a customized application form to get the details required from each applicant. 2) Post the "Innovation Call" on GTI®. 3) Market "Innovation Call" among various networks and channels. Optional services: a) Pitch Sessions (online) ? where all the shortlisted applicants will be invited to pitch to directly to the clients. b) Facilitation of physical workshops / hackathons with selected expert communities, in order to complement the call. c) Facilitation of virtual / off line meetings with the applicants. Optional services: 1) Pitch Sessions (online) - where all the shortlisted applicants will be invited to pitch to directly to the clients. 2) Facilitation of physical workshops / hackathons with selected expert communities, in order to complement the call. 3) Facilitation of virtual / off line meetings with the applicants. 4) Direct promotion of calls with domestic and international network. 5) Technical due diligence support to analyze and evaluate submitted solutions, ex. with regards to their climate risk mitigation potential. 6) Technology benchmarking and comparative analysis 7) Evaluating technologies and business models for localized deployment strategies. Methodical and technical support in the implementation phase of the selected technical solution. Previous Innovation Calls 1) Wet Waste Management technology /solutions - Deadline 31st January 2017 (Closed) A large Global multinational corporate is seeking Wet Waste Management technology solutions for the Indian market. They are one of the world?s leading suppliers of technology and services and they are seeking to collaborate with start-ups, SMEs, corporates and R&D organizations to exclusively commercialize decentralized solutions (to be used at individual homes or in communities such as apartment complexes) that can treat kitchen waste at source in India. Possible forms of collaboration: Joint development License/sale of patent or IP to develop products Collaborative R&D Contract manufacturing Criteria: User friendliness ? minimal intervention from the user Cost effective Localized in scale Odorless Compact solution - integrated with the kitchen work flow Accelerated conversion process ? from waste to resource. Outcomes: GBI scouted and invited interested start-ups, SMEs and R&D organizations to apply for the call with an aim to partner with the client to deploy Decentralized Wet Waste Management products/solutions commercially in India. GBI shortlisted 24 companies / start-ups (11 International and 13 India) who have shown various levels of interest to collaborate with the client. GBI also provided a bench marking study with a comparative analysis of the various Wet Waste Management technologies identified during primary and secondary research.';
export const INNOVATION_FULL_CONTENT_STEP =
  '1) Develop a customized scope and objective of the "Innovation Call" along with the client, including: ' +
  "\n" +
  "   a) Describing the benefits for applicants." +
  "\n" +
  "   b) Defining eligibility criteria for applicants (clear value proposition, size, stage, amount of funding raised, etc.). " +
  "\n" +
  "   c) Designing a customized application form to get the details required from each applicant. " +
  "\n" +
  '2) Post the "Innovation Call" on GTI®. ' +
  "\n" +
  '3) Market "Innovation Call" among various networks and channels. Optional services: ' +
  "\n" +
  "   a) Pitch Sessions (online) ? where all the shortlisted applicants will be invited to pitch to directly to the clients." +
  "\n" +
  "   b) Facilitation of physical workshops / hackathons with selected expert communities, in order to complement the call." +
  "\n" +
  "   c) Facilitation of virtual / off line meetings with the applicants. ";
export const INNOVATION_FULL_CONTENT_OPTIONAL =
  "1) Pitch Sessions (online) - where all the shortlisted applicants will be invited to pitch to directly to the clients." +
  "\n" +
  "2) Facilitation of physical workshops / hackathons with selected expert communities, in order to complement the call. " +
  "\n" +
  "3) Facilitation of virtual / off line meetings with the applicants. " +
  "\n" +
  "4) Direct promotion of calls with domestic and international network." +
  "\n" +
  "5) Technical due diligence support to analyze and evaluate submitted solutions, ex. with regards to their climate risk mitigation potential. " +
  "\n" +
  "6) Technology benchmarking and comparative analysis " +
  "\n" +
  "7) Evaluating technologies and business models for localized deployment strategies. Methodical and technical support" +
  "\n" +
  " in the implementation phase of the selected technical solution. ";
export const INNOVATION_FULL_CONTENT_PREVIOUS =
  "1) Wet Waste Management technology /solutions - Deadline 31st January 2017 (Closed)" +
  "\n" +
  "A large Global multinational corporate is seeking Wet Waste Management technology solutions for the Indian market. They are one of" +
  "\n" +
  " the world's leading suppliers of technology and services and they are seeking to collaborate with start-ups, SMEs, corporates" +
  "\n" +
  " and R&D organizations to exclusively commercialize decentralized solutions (to be used at individual homes or in communities such" +
  "\n" +
  " as apartment complexes) that can treat kitchen waste at source in India.";
export const INNOVATION_FULL_CONTENT_POSSIBLE =
  "Joint development License/sale of patent or IP to develop products Collaborative R&D Contract manufacturing Criteria:" +
  "\n" +
  " User friendliness , minimal intervention from the user Cost effective Localized in scale Odorless Compact solution " +
  "\n" +
  "- integrated with the kitchen work flow Accelerated conversion process , from waste to resource.";
export const INNOVATION_FULL_CONTENT_OUTCOME =
  "GBI scouted and invited interested start-ups, SMEs and R&D organizations to apply for the call with an aim to partner with " +
  "\n" +
  "the client to deploy Decentralized Wet Waste Management products/solutions commercially in India. GBI shortlisted 24" +
  "\n" +
  " companies / start-ups (11 International and 13 India) who have shown various levels of interest to collaborate with the " +
  "\n" +
  " client. GBI also provided a bench marking study with a comparative analysis of the various Wet Waste Management " +
  "\n" +
  " technologies identified during primary and secondary research.";

export const premiumServicesDetails = {
  marketInsightReport: {
    title: "Market Insight report",
    description:
      "Market insight is discovering a relevant, actionable and previously unrealized reality about a target market as the result of deep, subjective data analysis. Understanding the market aims to benefit both parties, meeting your target audience's actual needs and wants while simultaneously profiting. The market insight report will consist of the generic view of the market, technology, local policies, and the product's viability in the market.",
  },
  detailedMarketInsightAndTechnologyValidationeReport: {
    title: "Detailed Market Insight and Technology validation report",
    description:
      "Detailed market insight & technology validation offers more extensive research into the target market & how technology, if deployed, will perform in the market. The detailed market insight report will consist of Market analysis, technology validation, competitor analysis, Primary research feedback from the market about the product, Stakeholder identification, Market entry strategy, Local policies and barriers to the product or technology and recommendations in depth.",
  },
  identificationAndFacilitationOfMeetings: {
    title:
      "Identification and facilitation of meetings with potential partners/customers/investors",
    description:
      "In case of higher requirements please reach out to us directly. GTI will help you find the right partner by understanding your requirements and curating one-on-one connections and business meetings with potential partners, customers or investors.",
  },
  technologyScouting: {
    title: "Technology Scouting",
    description:
      "GTI will assist in scouting and identification of technologies that will meet your specific business requirements.",
  },
  openInnovationChallenge: {
    title: "Open Innovation Challenge",
    description:
      "GTI will host an open innovation challenge where innovative technology providers can apply to be considered for pitching to your company in order to find the right partner for your business innovation needs. ",
  },
  additionalValueAddedServices1: {
    title: "Additional value added services",
    description:
      "Benchmarking and comparative analysis to provide recommendations: Provide an in-depth analysis by benchmarking the final list of shortlisted companies",
  },
  additionalValueAddedServices2: {
    title: "Additional value added services",
    description:
      "Employee engagement – workshops on Open Innovation: Workshops to build the capacities for leadership teams which will consist of design thinking workshops, presentation of use cases to startups, Technology transfer Ecosystem development for technology deployment and startup mentorship",
  },
  additionalValueAddedServices3: {
    title: "Additional value added services",
    description:
      "Facilitation of Technology pitch Sessions: facilitate the organization of 2day pitch events where startups can physically interact with leadership teams",
  },
  additionalValueAddedServices4: {
    title: "Additional value added services",
    description:
      "Support with Technology deployment: support with the organisation on a continuous basis to ensure the technology product / service is deployed sustainably",
  },
};

export type articleItem = {
  topic: string;
  description: string;
  shortDescription: string;
  sectorId: string;
  subSectorId: string;
  articleType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  webinarId: string;
};
export type productItem = {
  name: string;
  description: string;
  images: string[];
  sectorId: string;
  subSectorId: string;
  developmentStage: string;
  iprStatus: string[];
  videos?: string[];
  documents?: string[];
};

export type productUpdateItem = {
  productId: string;
  name: string;
  description: string;
  document: string;
  image: string;
  sectorId: string;
  subSectorId: string;
  developmentStage: string;
  iprStatus: string[];
  video?: string;
};

export type productItemFullFetched = {
  _id: string;
  name: string;
  description: string;
  image: string;
  images: string[];
  videos: string[];
  documents: string[];
  video: string;
  displayOnHomePage: Boolean;
  isApprovedBySubAdmin: Boolean;
  isApprovedByAdmin: Boolean;
  isRejected: Boolean;
  document: string;
  sectorId: string;
  subSectorId: string;
  userId: string;
  createdAt: string;
  developmentStage: string;
  iprStatus: [];
  __v: number;
  users: {
    _id: string;
    fullName: string;
    email: string;
    phoneNumber: string;
    countryCode: string;
    referenceCode: string;
    isEmailVerified: Boolean;
    isUserVerified: Boolean;
    isRejected: Boolean;
    password: string;
    userRole: number;
    companyId: string;
    follower: string[];
    following: string[];
    connections: [
      {
        connectionStatus: string;
        userId: string;
      }
    ];
    createdAt: string;
    __v: number;
  };
  company: {
    _id: string;
    name: string;
    logo: string;
    description: string;
    address: string;
    website: string;
    country: string;
    companyTurnover: number;
    companyId: string;
    typeAndSizeOfPartnersRequired: string[];
    typesOfPartnershipConsidered: string[];
    createdAt: string;
    __v: number;
  };
  sectors: {
    _id: string;
    name: string;
    slug: string;
    image: string;
    createdAt: string;
    __v: number;
  };
  subsectors: {
    _id: string;
    name: string;
    slug: string;
    sectorId: string;
    createdAt: string;
    __v: number;
  };
};
export type productItemPartialFetched = {
  _id: string;
  name: string;
  description: string;
  image: string;
  displayOnHomePage: Boolean;
  isApprovedBySubAdmin: Boolean;
  isApprovedByAdmin: Boolean;
  isRejected: Boolean;
  document: string;
  sectorId: string;
  subSectorId: string;
  userId: string;
  createdAt: string;
  developmentStage: string;
  iprStatus: string[];
  __v: number;
  sectors: {
    _id: string;
    name: string;
    slug: string;
    image: string;
    createdAt: string;
    __v: number;
  };
  subsectors: {
    _id: string;
    name: string;
    slug: string;
    sectorId: string;
    createdAt: string;
    __v: number;
  };
};

export type oppotunityItem = {
  description: string;
  technologyPartnerRequirement: string;
  documents: string[];
  images: string[];
  sectorId: string;
  subSectorId: string;
  name: string;
};
export type oppotunityUpdateItem = {
  opportunityId: string;
  description: string;
  technologyPartnerRequirement: string;
  document: string;
  image: string;
  sectorId: string;
  subSectorId: string;
};
export type oppotunityItemPartialFetched = {
  technologyPartnerRequirement: string;
  _id: string;
  description: string;
  image: string;
  displayOnHomePage: Boolean;
  isApprovedBySubAdmin: Boolean;
  isApprovedByAdmin: Boolean;
  isRejected: Boolean;
  document: string;
  sectorId: string;
  subSectorId: string;
  userId: string;
  createdAt: string;
  __v: number;
  sectors: {
    _id: string;
    name: string;
    slug: string;
    image: string;
    createdAt: string;
    __v: number;
  };
  subsectors: {
    _id: string;
    name: string;
    slug: string;
    sectorId: string;
    createdAt: string;
    __v: number;
  };
  company: {
    logo: string;
    name: string;
  };
};

export type oppotunityItemFullFetched = {
  _id: string;
  description: string;
  technologyPartnerRequirement: string;
  displayOnHomePage: Boolean;
  isApprovedBySubAdmin: Boolean;
  isApprovedByAdmin: Boolean;
  isRejected: Boolean;
  document: string;
  image: string;
  sectorId: string;
  subSectorId: string;
  userId: string;
  createdAt: string;
  documents: string[];
  images: string[];
  __v: number;
  users: {
    _id: string;
    fullName: string;
    email: string;
    phoneNumber: string;
    countryCode: string;
    referenceCode: string;
    isEmailVerified: Boolean;
    isUserVerified: Boolean;
    isRejected: Boolean;
    password: string;
    userRole: number;
    userType: string;
    companyId: string;
    follower: [];
    following: [];
    connections: [
      {
        connectionStatus: string;
        userId: string;
      }
    ];
    createdAt: string;
    __v: number;
  };
  company: {
    _id: string;
    name: string;
    logo: string;
    description: string;
    address: string;
    website: string;
    country: string;
    companyTurnover: string;
    companyId: string;
    typeAndSizeOfPartnersRequired: [];
    typesOfPartnershipConsidered: [];
    iprStatus: [];
    createdAt: string;
    __v: number;
  };
  sectors: {
    _id: string;
    name: string;
    slug: string;
    image: string;
    createdAt: string;
    __v: number;
  };
  subsectors: {
    _id: string;
    name: string;
    slug: string;
    sectorId: string;
    createdAt: string;
    __v: number;
  };
};
export type articleUpdateItem = {
  articleId: string;
  topic: string;
  description: string;
  shortDescription: string;
  sectorId: string;
  subSectorId: string;
  articleType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  webinarId: string;
};

export type articleItemFetched = {
  _id: string;
  topic: string;
  description: string;
  shortDescription: string;
  displayOnHomePage: Boolean;
  isDeleted: Boolean;
  sectorId: string;
  subSectorId: string;
  articleType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  webinarId: string;
  createdAt: string;
  __v: number;
};

export type eventItem = {
  topic: string;
  description: string;
  shortDescription: string;
  sectorId: string;
  subSectorId: string;
  eventType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  startDate: string;
  endDate: string;
  organizedBy: string;
  webinarKey: string;
  webinarRegistrationLink: string;
  webinarOrganizerKey: string;
};
export type eventUpdateItem = {
  eventId: string;
  topic: string;
  description: string;
  shortDescription: string;
  sectorId: string;
  subSectorId: string;
  eventType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  startDate: string;
  endDate: string;
  organizedBy: string;
  webinarKey: string;
  webinarRegistrationLink: string;
  webinarOrganizerKey: string;
};
export type eventItemsFetched = {
  _id: string;
  topic: string;
  description: string;
  shortDescription: string;
  sectorId: string;
  subSectorId: string;
  eventType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  startDate: string;
  endDate: string;
  organizedBy: string;
  webinarKey: string;
  webinarRegistrationLink: string;
  webinarOrganizerKey: string;
  createdAt: string;
  endTime: string;
  startTime: string;
  meetingLink: string;
  price: number;
  videoUrl: string;
  __v: number;
};

export type blogsUpdateItem = {
  blogsId: string;
  topic: string;
  description: string;
  shortDescription: string;
  sectorId: string;
  subSectorId: string;
  blogsType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  webinarId: string;
};
export type blogsItemFetched = {
  _id: string;
  topic: string;
  description: string;
  shortDescription: string;
  displayOnHomePage: Boolean;
  isDeleted: Boolean;
  sectorId: string;
  subSectorId: string;
  blogsType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  webinarId: string;
  createdAt: string;
  __v: number;
};
export type blogItem = {
  topic: string;
  description: string;
  shortDescription: string;
  sectorId: string;
  subSectorId: string;
  blogType: string;
  imageUrl: string;
  externalLink: string;
  youtubeLink: string;
  webinarId: string;
};

export type InnovationItem = {
  title: string;
  description: string;
  companyName: string;
  companyLogo: string;
  companyDocument: string;
  imageUrl: string;
  startDate: string;
  endDate: string;
};
export type InnovationUpdateItem = {
  innovationCallsId: string;
  title: string;
  description: string;
  companyName: string;
  companyLogo: string;
  companyDocument: string;
  imageUrl: string;
  startDate: string;
  endDate: string;
};
export type InnovationItemFetched = {
  _id: string;
  title: string;
  description: string;
  companyName: string;
  companyLogo: string;
  companyDocument: string;
  imageUrl: string;
  startDate: string;
  endDate: string;
  displayOnHomePage: false;
  isDeleted: false;
  createdAt: string;
  formLink: string;
  referenceCode: string;
  __v: number;
};
export type contactItem = {
  name: string;
  phoneNumber: string;
  email: string;
  message: string;
};
export type sectorCreateItem = {
  name: string;
  image: string;
};
export type sectorItem = {
  _id: string;
  name: string;
  slug: string;
  image: string;
  createdAt: string;
  __v: number;
};
export type subsectorCreateItem = {
  name: string;
  sectorId: string;
  image: string;
};
export type subsectorItem = {
  _id: string;
  name: string;
  slug: string;
  image: string;
  createdAt: string;
  __v: number;
};
export type subscribedItem = {
  email: string;
  createdAt: string;
};
export type partnerItem = {
  relation: string;
  name: string;
  image: string;
};
export type toastItem = {
  message: string;
  status: Boolean;
  type: Number;
};
export type partnerData = {
  company: string;
  relation: string;
  logo: string;
};
export type techPartner = {
  _id: string;
  relation: string;
  name: string;
  image: string;
  isDeleted: Boolean;
  createdAt: string;
  __v: number;
};
export type partnermodelItem = {
  show: Boolean;
  data: partnerData;
};
export type presignedData = {
  fileName: string;
  fileType: string;
  filePath: string;
};

export type sendchatItem = {
  receiver: string;
  message: string;
};
export type getchatItem = {
  _id: string;
  sender: {
    _id: string;
    email: string;
  };
  receiver: {
    _id: string;
    email: string;
  };
  message: string;
  createdAt: string;
  __v: number;
};
export type notificationItem = {
  _id: string;
  userId: string;
  isViewed: Boolean;
  title: string;
  description: string;
  image: string;
  link: string;
  createdAt: string;
  __v: number;
};
export type userconnectionsItem = {
  _id: string;
  image: string;
  name: string;
};

export type checkBox = {
  id: number;
  label: string;
  checked: boolean;
};

export type SocialMediaDetailsType = {
  type: string;
  link: string;
  text: string;
};

export const typeAndSizeOfPartners = [
  { id: 1, label: "Industry SME <= 10", checked: false },
  { id: 2, label: "Industry SME 11-49", checked: false },
  { id: 3, label: "Industry SME 50-249", checked: false },
  { id: 4, label: "Industry 250-499", checked: false },
  { id: 5, label: "Industry >500", checked: false },
  { id: 6, label: "Industry MNE >10", checked: false },
  { id: 7, label: "INVENTOR", checked: false },
  { id: 8, label: "OTHER", checked: false },
  { id: 9, label: "R & D institution", checked: false },
  { id: 10, label: "University", checked: false },
  { id: 11, label: "Startup", checked: false },
  { id: 12, label: "Corporate", checked: false },
  { id: 13, label: "Investor", checked: false },
];

export const typeOfPartnershipRequired = [
  { id: 1, label: "Acquisition Agreement", checked: false },
  { id: 2, label: "Commercial Agency Agreement", checked: false },
  {
    id: 3,
    label: "Commercial Agency Agreement with technical assistance",
    checked: false,
  },
  { id: 4, label: "Financial Agreement", checked: false },
  {
    id: 5,
    label: "Industry MNE >Franchise Agency Agreement",
    checked: false,
  },
  { id: 6, label: "Joint Venture Agreement", checked: false },
  { id: 7, label: "License Agreement", checked: false },
  { id: 8, label: "Manufacturing Agreement", checked: false },
  { id: 9, label: "Outsourcing Agreement", checked: false },
  { id: 10, label: "Reciprocal Production", checked: false },
  { id: 11, label: "Research Cooperation Agreement", checked: false },
  { id: 12, label: "Services Agreement", checked: false },
  { id: 13, label: "Subcontracting Agreement", checked: false },
  { id: 14, label: "Technical Cooperation Agreement", checked: false },
];

export const typeAndSizeOfClientOptions = [
  { key: "1", value: "Industry SME <= 10" },
  { key: "2", value: "Industry SME 11-49" },
  { key: "3", value: "Industry SME 50-249" },
  { key: "4", value: "Industry 250-499" },
  { key: "5", value: "Industry >500" },
  { key: "6", value: "Industry MNE >10" },
  { key: "7", value: "INVENTOR" },
  { key: "8", value: "OTHER" },
  { key: "9", value: "R & D institution" },
  { key: "10", value: "University" },
];

export const turnoverFundOptions = [
  { key: "1", value: "1M" },
  { key: "2", value: "1-10M" },
  { key: "3", value: "10-20M" },
  { key: "4", value: "20-50M" },
  { key: "5", value: "50-100M" },
  { key: "6", value: "100-250M" },
  { key: "7", value: "250-500M" },
  { key: "8", value: ">500M" },
];

export enum ConnectionStatus {
  CONNECTED = "CONNECTED",
  REQUESTED = "REQUESTED",
  PENDING = "PENDING",
}

export type connectionOptionsType = {
  key: string;
  value: ConnectionStatus;
};

export const connectionOptions: connectionOptionsType[] = [
  { key: "1", value: ConnectionStatus.CONNECTED },
  { key: "2", value: ConnectionStatus.REQUESTED },
  { key: "3", value: ConnectionStatus.PENDING },
];

export enum ProductStatus {
  APPROVED = "APPROVED",
  PENDING = "PENDING",
  REJECTED = "REJECTED",
}

export const iprStatus = [
  { id: 1, value: "Copyright", checked: false },
  { id: 2, value: "Design Rights", checked: false },
  { id: 3, value: "Exlusive Rights", checked: false },
  {
    id: 4,
    value: "Granted patent or patent application essentials ",
    checked: false,
  },
  {
    id: 5,
    value: "Other(Registered design, plant variety, etc.)",
    checked: false,
  },
  { id: 6, value: "Patent(s) applied for but not yet granted, checked: false" },
  { id: 7, value: "Patents granted", checked: false },
  { id: 8, value: "Secret Know-how", checked: false },
  { id: 9, value: "Trade Marks", checked: false },
  { id: 10, value: "Not Applicable", checked: false },
];

export const developmentStage = [
  {
    key: "1",
    value: "TRL 1 - Basic research: basic principles are observed and reported",
  },
  {
    key: "2",
    value:
      "TRL 2 - Applied research: technology concept and/or application formulated",
  },
  {
    key: "3",
    value: "TRL 3 - Critical function, proof of concept established",
  },
  {
    key: "4",
    value: "TRL 4 - Laboratory testing of prototype component or process",
  },
  { key: "5", value: "TRL 5 - Laboratory testing of integrated system" },
  { key: "6", value: "TRL 6 - Prototype system verified" },
  { key: "7", value: "TRL 7 - Integrated pilot system demonstrated" },
  { key: "8", value: "TRL 8 - System incorporated in commercial design" },
  { key: "9", value: "TRL 9 - System ready for full scale deployment" },
];

export const STORAGE_KEY = "GTI_data";

export type publicationFetched = {
  _id: string;
  topic: string;
  description: string;
  imageUrl: string;
  documentUrl: string;
  displayOnHomePage: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
};

export type newsFetched = {
  _id: string;
  title: string;
  link: string;
  shortDescription: string;
  description: string;
  imageUrl: string;
  author: string;
  metaDescription: string;
  enclosure: string;
  media: string;
  mediaCaptionUrl: string;
  mediaCaptionText: string;
  tags: string[];
  partnersite: string[];
  displayOnHomePage: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
};

export type publicationCreate = {
  topic: string;
  description: string;
  documentUrl: string;
  imageUrl: string;
};

export type publicationUpdate = {
  publicationId: string;
  topic: string;
  description: string;
  documentUrl: string;
  imageUrl: string;
};

export interface MyFormValues {
  name: string;
  description: string;
  sectorId: string;
  subSectorId: string;
  developmentStage: string;
  iprStatus: string[];
}

export type files = {
  image: Boolean;
  document: Boolean;
  imageFile: File[];
  documents: File[];
  videos: File[];
};

export type promotionFiles = {
  image: Boolean;
  imageFile: File[];
};

export type promotionsCreate = {
  title: string;
  description: string;
  images: string[];
  productId?: string | null;
  opportunityId?: string | null;
};

export type retryPromotionPaymentInterface = {
  promotionId: string;
};

export type promotionsProfileCreate = {
  id: string;
};

export type featuredItem = {
  _id: string;
  title: string;
  description: string;
  contentTitle: string;
  contentDescription: string;
  images: string[];
};

export type promotionsType = {
  _id: string;
  title: string;
  description: string;
  images: string[];
  userId: string;
  paymentStatus: string;
  verificationStatus: string;
  createdAt: string;
};

export type premiumServiceCreate = {
  title: string;
  description: string;
  timeline?: string;
  reportLength?: string;
  amount: number;
};

export const title = {
  HOMEPAGE: "Global Technology Interface",
  SIGN_UP: "Sign Up | GTI",
  TECHNOLOGY: "Technology | GTI",
  YOUR_TECHNOLOGIES: "Your Technologies | GTI",
  COMPANY_TECHNOLOGIES: "Company Technologies | GTI",
  OPPORTUNITY: "Opportunity | GTI",
  YOUR_OPPORTUNITIES: "Your Opportunities | GTI",
  COMPANY_OPPORTUNITIES: "Company Opportunities | GTI",
  INNOVATION_CALLS: "Innovation Calls | GTI",
  HELPDESK: "Helpdesk | GTI",
  LEARN_MORE: "Learn More | GTI",
  ARTICLES: "Articles | GTI",
  PUBLICATIONS: "Publications | GTI",
  NEWS: "News | GTI",
  EVENTS: "Events | GTI",
  MASTERCLASS: "Masterclass | GTI",
  NOTIFICATIONS: "Notifications | GTI",
  CHAT: "Chat | GTI",
  CONTACT: "Contact | GTI",
  TERMS_AND_CONDITIONS: "Terms & Conditions | GTI",
  PRIVACY_POLICY: "Privacy Policy | GTI",
  PAGE_NOT_FOUND: "Page Not Found | GTI",
  PROMOTIONS: "Featured | GTI",
  REFUND_AND_CANCELLATION_POLICY: "Refund & Cancellation Policy | GTI",
  PREMIUM_SERVICES: "GTI Premium Services | GTI",
  DISPLAYER_PREMIUM_SERVICES: "GTI Services for Technology Displayers | GTI",
  SCOUTER_PREMIUM_SERVICES: "GTI Services for Technology Scouters | GTI",
  PRICING: "Pricing Plans | GTI",
};

export const metaData = {
  HOMEPAGE:
    "A platform to showcase innovative technologies and market opportunities. Access our global network to make meaningful partnerships and explore significant collaboration opportunities, all in one place",
  SIGN_UP:
    "Sign Up on GTI. A platform to showcase innovative technologies and market opportunities",
  TECHNOLOGY:
    "Use our platform to showcase your innovative technology, connect with relevant stakeholders, access market opportunities and grow your business. We feature your unique technologies on our social media pages, boosting visibility and accelerating connections with partners and customers",
  YOUR_TECHNOLOGIES:
    " Use our platform to showcase your innovative technology, connect with relevant stakeholders, access market opportunities and grow your business. We feature your unique technologies on our social media pages, boosting visibility and accelerating connections with partners and customers",
  COMPANY_TECHNOLOGIES:
    " Use our platform to showcase your innovative technology, connect with relevant stakeholders, access market opportunities and grow your business. We feature your unique technologies on our social media pages, boosting visibility and accelerating connections with partners and customers",
  OPPORTUNITY:
    "Governments, corporates and enterprises can leverage the GTI platform with the goal of scouting for innovative technologies, intellectual properties (IP), technologies and solutions in order to improve their existing businesses",
  YOUR_OPPORTUNITIES:
    "Governments, corporates and enterprises can leverage the GTI platform with the goal of scouting for innovative technologies, intellectual properties (IP), technologies and solutions in order to improve their existing businesses",
  COMPANY_OPPORTUNITIES:
    "Governments, corporates and enterprises can leverage the GTI platform with the goal of scouting for innovative technologies, intellectual properties (IP), technologies and solutions in order to improve their existing businesses",
  INNOVATION_CALLS:
    "Organizations can source innovative ideas and solutions globally to address specific needs and challenges",
  HELPDESK:
    "The objective of the GTI Helpdesk is to help companies with international market access in India, EU, UK, USA, Africa, and Southeast Asia. Through the helpdesk, companies looking at internationalisation can avail continuous and consistent support with their queries related to international market access and associated services. GTI provides online marketplace and virtual connect with potential partners / stakeholders",
  LEARN_MORE:
    "The Global Technology Interface® (GTI®) is an online platform for companies, entrepreneurs, MSMEs, startups and R&D institutions to showcase innovative technologies and facilitate collaborations in technology, science, research, and business on a global platform in a sustained manner. GTI® functions as a global market place where biotech, clean-tech and ICT entities can identify partners, investors and customers across geographic boundaries",
  ARTICLES:
    "GTI® functions as an educational and knowledge sharing platform by regularly sharing information and news from the industry through the platform. It is also an open innovation platform where entities can identify customers, investors and partners for innovations in cleantech, biotech and ICT, across geographic boundaries",
  PUBLICATIONS:
    "Discover a wealth of knowledge through our diverse range of publications. Explore insightful articles, research papers, and thought-provoking content that cover a variety of topics. Stay informed and engaged with our latest publications",
  NEWS: "Stay informed with our latest news and updates. Explore breaking stories, in-depth analysis, and timely reports on a variety of topics. Get the most recent information to stay ahead and make informed decisions",
  EVENTS:
    "GTI® team regularly hosts curated events to help governments, corporates and enterprises gain access to new markets, technologies, facilitate innovation partnerships and participate in research collaborations. To facilitate technology partnerships, knowledge exchange, education and capacity building, GTI® team hosts these events with hands-on support and assistance from our team to seamlessly and efficiently take forward your business needs. See details of upcoming events below!",
  NOTIFICATIONS:
    "Customize and manage your notifications with ease. Stay in control of updates, alerts, and important messages. Tailor your notification preferences to ensure you receive the information that matters most to you",
  CHAT: "Engage in real-time conversations with our chat feature. Connect with others, share ideas, and build meaningful connections. Enjoy a seamless and interactive communication experience with our user-friendly chat platform",
  CONTACT:
    "Reach out to us easily through our contact page. Whether you have questions, feedback, or inquiries, our team is here to assist you. Fill out the contact form, and we'll get back to you promptly",
  TERMS_AND_CONDITIONS:
    "Read and understand our terms and conditions. Learn about the guidelines and rules governing the use of our services. By accessing and using our website, you agree to abide by these terms for a positive and secure user experience",
  PRIVACY_POLICY:
    "Learn about how we collect, use, and protect your personal information. Our privacy policy outlines the principles and practices we follow to ensure your data is treated with care and in compliance with privacy regulations",
  USER_PROFILE:
    "View and manage your user profile. Personalize your account settings, update information, and customize your experience. Explore features to enhance your user profile and make the most out of your interactions on our platform",
  PAGE_NOT_FOUND:
    "Oops! The page you are looking for could not be found. Explore other areas of our site or contact support for assistance. We apologize for any inconvenience",
  PROMOTIONS:
    "We're pleased to announce our global promotion within the esteemed network of GTI. Your company will now be prominently featured on the GTI homepage, reaching a vast audience through our Global Media partners' news stream and across all our social media platforms. This enhanced visibility opens doors for exploring new partnerships on our platform, facilitating technology access and deployment within the clean tech ecosystem at just $20/month. We're committed to strengthening our relationship with you and fostering global collaborations to propel your company's growth. Get ready to shine on a global stage as we showcase your innovative solutions and forge impactful partnerships together!  ",
  REFUND_AND_CANCELLATION_POLICY: "REFUND_AND_CANCELLATION_POLICY",
  PREMIUM_SERVICES:
    "GTI Market Access and Innovation Services - Comprehensive support for technology companies and scouters to accelerate innovation and market entry",
  DISPLAYER_PREMIUM_SERVICES:
    "GTI Market Access and Innovation Services for Technology Displayers - Comprehensive support for technology companies, entrepreneurs, MSMEs, startups, and R&D institutions to deploy and validate their technology in new markets",
  SCOUTER_PREMIUM_SERVICES:
    "GTI Market Access and Innovation Services for Technology Scouters - Customized assistance for finding technology partners for collaborations or investments through our comprehensive scouting platform",
  PRICING:
    "Choose the perfect plan for your business needs. Compare our Free, Basic, and Pro plans with transparent pricing and comprehensive features for technology showcasing, networking, and business growth.",
};

export const FEATURED =
  "We're pleased to announce our global promotion within the esteemed network of GTI. Your company will now be prominently featured on the GTI homepage, reaching a vast audience through our Global Media partners' news stream and across all our social media platforms. This enhanced visibility opens doors for exploring new partnerships on our platform, facilitating technology access and deployment within the clean tech ecosystem at just $20/month. We're committed to strengthening our relationship with you and fostering global collaborations to propel your company's growth. Get ready to shine on a global stage as we showcase your innovative solutions and forge impactful partnerships together!";
