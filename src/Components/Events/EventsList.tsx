import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import "./style.css";
import { useSelector } from "react-redux";
import eventbanner from "../../assests/banners/eventbanner.png";
import ReactPlayer from "react-player";
import { eventItemsFetched, LIMIT, NONE } from "../constants";
import { useNavigate } from "react-router-dom";
import { getEvents } from "../../store/actioncreators/eventactionss";
import eventsDefault from "../../assests/images/events-default.jpg";
import { getQueryParams } from "../../utils";

const Card = ({ item }: { item: eventItemsFetched }) => {
  const DOS = new Date(item.startDate);
  const DOE = new Date(item.endDate);
  const navigate = useNavigate();

  const handleView = () => {
    navigate(`/events/${item._id}`, { state: { id: item._id } });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleString("default", {
      month: "short",
      day: "2-digit",
      year: "numeric",
    });
  };

  const isEventPaid = item.price && item.price > 0;
  const eventStatus = DOS > new Date() ? "upcoming" : "ongoing";

  return (
    <div className="event-card-main" onClick={handleView}>
      <div className="event-card-img">
        <img
          src={item?.imageUrl ? item?.imageUrl : eventsDefault}
          alt={item?.topic?.replace(/(<([^>]+)>)/gi, "") || "Event"}
          loading="lazy"
        />
        {/* Event Status Badge */}
        <div className="absolute top-4 left-4">
          <span
            className={`px-3 py-1 text-xs font-semibold rounded-full ${
              eventStatus === "upcoming"
                ? "bg-green-100 text-green-800"
                : "bg-blue-100 text-blue-800"
            }`}
          >
            {eventStatus === "upcoming" ? "Upcoming" : "Live"}
          </span>
        </div>
        {/* Price Badge */}
        {isEventPaid && (
          <div className="absolute top-4 right-4">
            <span className="bg-GTI-BLUE-default text-white px-3 py-1 text-xs font-semibold rounded-full">
              ${item.price}
            </span>
          </div>
        )}
      </div>

      <div className="event-card-content">
        <div className="event-card-title">
          <h3
            className="text-xl font-bold text-gray-900 line-clamp-2 leading-tight"
            dangerouslySetInnerHTML={{
              __html: item.topic?.replace(/(<([^>]+)>)/gi, "") || "",
            }}
          ></h3>

          {/* Event Type */}
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <span className="font-medium">{item.eventType || "Event"}</span>
          </div>
        </div>

        {/* Event Dates */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <span>
              <span className="font-medium">Starts:</span> {formatDate(DOS)}
            </span>
          </div>

          {DOE.toString() !== "Invalid Date" &&
            DOS.getTime() !== DOE.getTime() && (
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <span>
                  <span className="font-medium">Ends:</span> {formatDate(DOE)}
                </span>
              </div>
            )}
        </div>

        {/* Organizer */}
        {item.organizedBy && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
            <span>
              <span className="font-medium">By:</span> {item.organizedBy}
            </span>
          </div>
        )}

        {/* Action Button */}
        <div className="event-card-button pt-2">
          <button className="w-full bg-GTI-BLUE-default text-white font-semibold py-3 px-4 rounded-xl hover:bg-blue-800 transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
            {isEventPaid ? `Register for $${item.price}` : "View Details"}
          </button>
        </div>
      </div>
    </div>
  );
};

const EventList = ({
  skip: parentSkip,
  limit: parentLimit,
  eventType,
}: {
  skip: string;
  limit: string;
  eventType: string;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const events: EVENT = useSelector((state: STATE) => state.EVENT.EVENT);

  const navigate = useNavigate();
  const skip = getQueryParams("skip") ?? "0";
  const [page, setPage] = useState({
    skip: "0",
    limit: LIMIT,
  });
  const [maxSkip, setMaxSkip] = useState(0);

  useEffect(() => {
    setMaxSkip(Math.ceil(events.EVENT_LIST.eventsCount / 9));
  }, [page, events.EVENT_LIST.eventsCount]);

  const fetchData = (val: number) => {
    let newSkip = parseInt(page.skip) + val;
    if (newSkip >= 0) {
      navigate(`/events?skip=${newSkip}`);
      setPage({
        skip: newSkip.toString(),
        limit: page.limit,
      });
      dispatch(getEvents(skip, page.limit, eventType));
    }
  };

  useEffect(() => {
    dispatch(getEvents(skip, page.limit, eventType));
    setPage({
      skip: skip ? skip : "0",
      limit: page.limit,
    });
    window.scrollTo(0, 0);
  }, [skip, eventType, dispatch, page.limit]);

  return (
    <div className="w-full">
      {/* Events Grid */}
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {events.EVENT_LIST.events.length > 0 ? (
          <div className="event-list-main">
            {events.EVENT_LIST.events.map(
              (item: eventItemsFetched, id: number) => {
                return <Card item={item} key={id} />;
              }
            )}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <svg
                className="mx-auto h-16 w-16 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                No events found
              </h3>
              <p className="mt-2 text-gray-500">
                There are no events available for the selected filter. Try
                changing your filter or check back later.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Modern Pagination */}
      {events.EVENT_LIST.events.length > 0 && maxSkip > 1 && (
        <div className="flex justify-center mt-12 mb-8">
          <nav className="flex items-center space-x-2">
            <button
              disabled={page.skip === "0"}
              onClick={() => fetchData(-1)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-GTI-BLUE-default disabled:text-gray-400 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 disabled:hover:bg-white disabled:cursor-not-allowed transition-all duration-200"
            >
              <svg
                className="mr-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Previous
            </button>

            {/* Page Numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, maxSkip) }, (_, i) => {
                const pageNum = parseInt(page.skip) + i + 1;
                if (pageNum <= maxSkip) {
                  return (
                    <button
                      key={i}
                      onClick={() => fetchData(i)}
                      className={`px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 ${
                        i === 0
                          ? "bg-GTI-BLUE-default text-white shadow-lg"
                          : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
                return null;
              })}

              {maxSkip > parseInt(page.skip) + 5 && (
                <>
                  <span className="px-2 text-gray-500">...</span>
                  <button
                    onClick={() => fetchData(maxSkip - parseInt(page.skip) - 1)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-all duration-200"
                  >
                    {maxSkip}
                  </button>
                </>
              )}
            </div>

            <button
              disabled={
                (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                events.EVENT_LIST.eventsCount
              }
              onClick={() => fetchData(1)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-GTI-BLUE-default disabled:text-gray-400 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 disabled:hover:bg-white disabled:cursor-not-allowed transition-all duration-200"
            >
              Next
              <svg
                className="ml-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </nav>
        </div>
      )}

      {/* Events Count */}
      {events.EVENT_LIST.events.length > 0 && (
        <div className="text-center text-sm text-gray-600 mb-8">
          Showing {events.EVENT_LIST.events.length} of{" "}
          {events.EVENT_LIST.eventsCount} events
        </div>
      )}
    </div>
  );
};

export default EventList;
