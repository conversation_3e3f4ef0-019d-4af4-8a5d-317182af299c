import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Helmet } from "react-helmet";
import { useDetectClickOutside } from "react-detect-click-outside";
import { CalendarDaysIcon, SparklesIcon } from "@heroicons/react/24/solid";

import events from "../../assests/images/events.png";
import { LIMIT, SKIP, title, metaData } from "../constants";
import EventList from "./EventsList";
import { getSector } from "../../store/actioncreators/sectoractions";
import { getSubSector } from "../../store/actioncreators/sub-sectoractions";

const Events = () => {
  const dispatch: Dispatch<any> = useDispatch();

  useEffect(() => {
    dispatch(getSector());
    dispatch(getSubSector());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const eventTypeList = ["Online", "Offline", "Videos"];

  const [page, setPage] = useState({
    skip: SKIP,
    limit: LIMIT,
  });

  const [eventType, setEventType] = useState({
    drop: false,
    selected: "All Events",
    id: "",
  });

  const ref1 = useDetectClickOutside({
    onTriggered: () => {
      setEventType({ ...eventType, drop: false });
    },
  });

  useEffect(() => {
    setEventType({
      ...eventType,
      selected: "All Events",
      id: "",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-col w-full min-h-screen bg-gray-50">
      <Helmet>
        <title>{title.EVENTS}</title>
        <meta name="description" key="description" content={metaData.EVENTS} />
        <meta name="title" key="title" content="Events" />
        <meta property="og:title" content="Events" />
        <meta property="og:description" content={metaData.EVENTS} />
        <meta property="og:image" content={events} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/events`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Events" />
        <meta name="twitter:description" content={metaData.EVENTS} />
        <meta name="twitter:image" content={events} />
        <meta name="twitter:card" content="Events" />
      </Helmet>

      {/* Modern Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-GTI-BLUE-default via-blue-700 to-indigo-800">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
          <div className="text-center">
            <div className="flex justify-center items-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
                <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                  <CalendarDaysIcon className="h-12 w-12 md:h-16 md:w-16 text-white" />
                </div>
              </div>
            </div>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
              <span className="block">Innovation</span>
              <span className="block bg-gradient-to-r from-blue-200 to-cyan-200 bg-clip-text text-transparent">
                Events
              </span>
            </h1>

            <p className="max-w-3xl mx-auto text-lg md:text-xl text-blue-100 leading-relaxed mb-8">
              GTI® team regularly hosts curated events to help governments,
              corporates and enterprises gain access to new markets,
              technologies, facilitate innovation partnerships and participate
              in research collaborations.
            </p>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-blue-200">
              <div className="flex items-center gap-2">
                <SparklesIcon className="h-4 w-4" />
                <span>Innovation Partnerships</span>
              </div>
              <div className="flex items-center gap-2">
                <SparklesIcon className="h-4 w-4" />
                <span>Technology Collaborations</span>
              </div>
              <div className="flex items-center gap-2">
                <SparklesIcon className="h-4 w-4" />
                <span>Global Networks</span>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-white/3 rounded-full blur-3xl"></div>
        </div>
      </div>

      {/* Modern Filter Section */}
      <div className="events-filters-container">
        <div className="events-filters-content">
          <div className="events-filter-dropdown" ref={ref1}>
            <button
              id="dropdownDefault"
              data-dropdown-toggle="dropdown"
              className="events-filter-button"
              type="button"
              onClick={() => {
                setEventType({ ...eventType, drop: !eventType.drop });
              }}
            >
              <span>{eventType.selected}</span>
              <svg
                className={`w-4 h-4 transition-transform duration-200 ${
                  eventType.drop ? "rotate-180" : ""
                }`}
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            {eventType.drop && (
              <div className="events-filter-dropdown-menu">
                <div
                  className="events-filter-dropdown-item"
                  onClick={() => {
                    setEventType({
                      drop: false,
                      selected: "All Events",
                      id: "",
                    });
                  }}
                >
                  All Events
                </div>
                {eventTypeList &&
                  eventTypeList.map((item, id) => {
                    return (
                      <div
                        key={id}
                        className="events-filter-dropdown-item"
                        onClick={() => {
                          setEventType({
                            id: id.toString(),
                            drop: false,
                            selected: item,
                          });
                        }}
                      >
                        {item}
                      </div>
                    );
                  })}
              </div>
            )}
          </div>

          <div className="text-sm text-gray-600">
            Showing events for:{" "}
            <span className="font-semibold text-GTI-BLUE-default">
              {eventType.selected}
            </span>
          </div>
        </div>
      </div>

      {/* Events List */}
      <div className="flex-1 py-8">
        <EventList
          skip={page.skip}
          limit={page.limit}
          eventType={eventType.selected}
        />
      </div>
    </div>
  );
};
export default Events;
