import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import axios from "axios";

import { store } from "../../store";
import { eventItemsFetched } from "../constants";
import { RequestMethods } from "../../shared/RequestMethods";

const EventVideo = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const { id } = useParams();
  const [videoUrl, setVideoUrl] = useState("");
  const [isUserRegistered, setIsUserRegistered] = useState(false);
  const navigate = useNavigate();

  const state = {
    id: id ?? "",
  };

  let [event, setEvent] = useState<eventItemsFetched>({
    _id: "",
    topic: "",
    description: "",
    shortDescription: "",
    sectorId: "",
    subSectorId: "",
    eventType: "",
    imageUrl: "",
    startDate: "",
    endDate: "",
    externalLink: "",
    youtubeLink: "",
    webinarKey: "",
    webinarOrganizerKey: "",
    organizedBy: "",
    webinarRegistrationLink: "",
    createdAt: "",
    startTime: "",
    endTime: "",
    meetingLink: "",
    price: 0,
    videoUrl: "",
    __v: -1,
  });

  const loadEvent = (id: string) => {
    var config = {
      method: RequestMethods.GET,
      url: `${process.env.REACT_APP_BASE_API}/events/eventdetails/${id}`,
      headers: {
        Authorization: `Bearer ${store.getState().USER.USER.token}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setEvent(response.data);
        console.log(response?.data?.videoUrl);
        setVideoUrl(response?.data?.videoUrl);
      })
      .catch(function (error) {});
  };

  const getRegistration = () => {
    const token = localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    let config = {
      method: "GET",
      url: `${process.env.REACT_APP_BASE_API}/events/isUserRegistered/${state.id}`,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setIsUserRegistered(response?.data);
      })
      .catch(function (error) {});
  };

  useEffect(() => {
    loadEvent(state.id);
    getRegistration();
  }, [getRegistration, state.id]);

  useEffect(() => {
    setVideoUrl(event?.videoUrl);
  }, [event]);

  return (
    <React.Fragment>
      <div className="event-video">
        {videoUrl && isUserRegistered ? (
          <div className="event-video-container">
            <div className="event-video-header">
              <h1
                className="event-video-title"
                dangerouslySetInnerHTML={{ __html: event.topic || "" }}
              ></h1>
              <div className="event-video-description">
                <div
                  dangerouslySetInnerHTML={{ __html: event.description }}
                ></div>
              </div>
            </div>

            <div className="event-video-player">
              <video
                className="w-full h-auto rounded-2xl"
                controls
                controlsList="nodownload"
                style={{ maxHeight: "70vh" }}
              >
                <source src={videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>

            <div className="text-center mt-8">
              <button
                onClick={() => navigate(`/events/${event._id}`)}
                type="button"
                className="bg-GTI-BLUE-default text-white font-semibold py-3 px-6 rounded-xl hover:bg-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Back to Event Details
              </button>
            </div>
          </div>
        ) : isUserRegistered ? (
          <div className="min-h-screen flex items-center justify-center">
            <div className="max-w-md mx-auto text-center p-8 bg-white rounded-2xl shadow-xl">
              <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 15v2m0 0v2m0-2h2m-2 0H10m9-7a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Access Denied
              </h2>
              <p className="text-gray-600 mb-8 leading-relaxed">
                You need to register for this event to access the video content.
                Please register first to enjoy the full event experience.
              </p>

              <button
                onClick={() => navigate(`/events/${event._id}`)}
                type="button"
                className="w-full bg-GTI-BLUE-default text-white font-semibold py-3 px-6 rounded-xl hover:bg-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Register for Event
              </button>
            </div>
          </div>
        ) : (
          <div className="min-h-screen flex items-center justify-center">
            <div className="max-w-md mx-auto text-center p-8 bg-white rounded-2xl shadow-xl">
              <div className="w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>

              <h2
                className="text-2xl font-bold text-gray-900 mb-4"
                dangerouslySetInnerHTML={{ __html: event.topic || "Event" }}
              ></h2>
              <p className="text-gray-600 mb-8 leading-relaxed">
                Please log in and register for this event to access the content.
              </p>

              <button
                onClick={() => navigate(`/events/${event._id}`)}
                type="button"
                className="w-full bg-GTI-BLUE-default text-white font-semibold py-3 px-6 rounded-xl hover:bg-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                View Event Details
              </button>
            </div>
          </div>
        )}
      </div>
    </React.Fragment>
  );
};

export default EventVideo;
