import React from "react";
import { SparklesIcon } from "@heroicons/react/24/outline";
import featured from "../../assests/images/featured/featured.svg";
import instagram from "../../assests/images/featured/instagram.svg";
import facebook from "../../assests/images/featured/facebook.svg";
import linkedin from "../../assests/images/featured/linkedin.svg";
import twitter from "../../assests/images/featured/twitter.svg";
import economico_pro from "../../assests/images/featured/economico-pro.svg";
import cafe_europe from "../../assests/images/featured/cafe-europe.svg";
import circular_times from "../../assests/images/featured/circular-times.svg";
import punkt_info from "../../assests/images/featured/punkt-info.svg";
import swiss_trade from "../../assests/images/featured/swiss-trade.svg";

const gti_homepage = "/images/featured/git-homepage.svg";

const PromotionalPlatforms: React.FC = () => {
  return (
    <div className="mt-20">
      <div className="text-center mb-16">
        <div className="flex justify-center mb-6">
          <div className="bg-GTI-BLUE-default/10 rounded-2xl p-3">
            <SparklesIcon className="h-8 w-8 text-GTI-BLUE-default" />
          </div>
        </div>
        <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-GTI-BLUE-default to-gray-900 bg-clip-text text-transparent mb-6">
          Global Promotional Network
        </h2>
        <div className="w-24 h-1 bg-gradient-to-r from-GTI-BLUE-default to-purple-500 mx-auto mb-6 rounded-full"></div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Your content will be showcased across our extensive network of premium
          platforms, reaching millions of potential customers worldwide
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-16">
        {/* Enhanced GTI Homepage Card */}
        <div className="group bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="relative bg-gradient-to-br from-blue-500 to-blue-700 p-8">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-white/20 rounded-full px-4 py-2">
                <span className="text-white text-sm font-medium">Homepage</span>
              </div>
              <img
                src={featured}
                className="h-8 w-8 text-white opacity-80"
                alt="Featured"
              />
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">GTI Platform</h3>
            <p className="text-blue-100 leading-relaxed">
              Prime visibility on our main platform with featured placement and
              maximum exposure
            </p>
          </div>
          <div className="relative p-8">
            <img
              src={gti_homepage}
              className="w-full rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500"
              alt="GTI Homepage"
            />
          </div>
        </div>

        {/* Enhanced Social Media Card */}
        <div className="group bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="relative bg-gradient-to-br from-purple-500 to-purple-700 p-8">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-white/10 rounded-full px-4 py-2">
                <span className="text-white text-sm font-medium">
                  Multi-Platform
                </span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">
              Social Media Network
            </h3>
            <p className="text-purple-100 leading-relaxed">
              Amplify your reach across all major social platforms with targeted
              campaigns and engaging content
            </p>
          </div>
          <div className="relative p-8">
            <div className="bg-gray-50 rounded-xl p-6">
              <p className="text-gray-600 leading-relaxed text-lg">
                We will be promoting your technology on our social media
                platforms, significantly enhancing your online presence and
                reaching millions of potential customers worldwide.
              </p>
              <div className="flex justify-center space-x-6 mt-6">
                <img
                  src={instagram}
                  className="h-8 w-8 opacity-70 hover:opacity-100 transition-opacity duration-200 transform hover:scale-110"
                  alt="Instagram"
                />
                <img
                  src={facebook}
                  className="h-8 w-8 opacity-70 hover:opacity-100 transition-opacity duration-200 transform hover:scale-110"
                  alt="Facebook"
                />
                <img
                  src={linkedin}
                  className="h-8 w-8 opacity-70 hover:opacity-100 transition-opacity duration-200 transform hover:scale-110"
                  alt="LinkedIn"
                />
                <img
                  src={twitter}
                  className="h-8 w-8 opacity-70 hover:opacity-100 transition-opacity duration-200 transform hover:scale-110"
                  alt="Twitter"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Partner Publications */}
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-8 md:p-12 border border-gray-200">
        <div className="text-center mb-12">
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Partner Publications
          </h3>
          <div className="w-20 h-1 bg-gradient-to-r from-GTI-BLUE-default to-purple-500 mx-auto mb-4 rounded-full"></div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Featured placement in leading industry publications and media
            outlets
          </p>
        </div>
        <div className="space-y-6">
          <div className="flex justify-center">
            <a
              href="https://cafe-europe.info/en.html"
              target="_blank"
              rel="noreferrer"
              className="group block w-2/3 p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl hover:from-gray-100 hover:to-gray-200 transition-all duration-300 transform hover:scale-105 hover:shadow-lg border border-gray-200/50"
            >
              <img
                src={cafe_europe}
                className="w-full group-hover:scale-105 transition-transform duration-300"
                alt="Cafe Europe"
              />
            </a>
          </div>
          <div className="grid grid-cols-2 gap-6">
            <a
              href="https://economico.pro/"
              target="_blank"
              rel="noreferrer"
              className="group block p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl hover:from-gray-100 hover:to-gray-200 transition-all duration-300 transform hover:scale-105 hover:shadow-lg border border-gray-200/50"
            >
              <img
                src={economico_pro}
                className="w-full group-hover:scale-105 transition-transform duration-300"
                alt="Economico Pro"
              />
            </a>
            <a
              href="https://punkt4.info/"
              target="_blank"
              rel="noreferrer"
              className="group block p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl hover:from-gray-100 hover:to-gray-200 transition-all duration-300 transform hover:scale-105 hover:shadow-lg border border-gray-200/50"
            >
              <img
                src={punkt_info}
                className="w-full group-hover:scale-105 transition-transform duration-300"
                alt="Punkt Info"
              />
            </a>
          </div>
          <div className="grid grid-cols-2 gap-6">
            <div className="group block p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl hover:from-gray-100 hover:to-gray-200 transition-all duration-300 transform hover:scale-105 hover:shadow-lg border border-gray-200/50">
              <img
                src={circular_times}
                className="w-full group-hover:scale-105 transition-transform duration-300"
                alt="Circular Times"
              />
            </div>
            <a
              href="https://swisstrade.com/"
              target="_blank"
              rel="noreferrer"
              className="group block p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl hover:from-gray-100 hover:to-gray-200 transition-all duration-300 transform hover:scale-105 hover:shadow-lg border border-gray-200/50"
            >
              <img
                src={swiss_trade}
                className="w-full group-hover:scale-105 transition-transform duration-300"
                alt="Swiss Trade"
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionalPlatforms;
