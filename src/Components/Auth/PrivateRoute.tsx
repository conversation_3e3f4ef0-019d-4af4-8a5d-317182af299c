import React, { useEffect } from "react";
import { useAuth } from "../../hooks/useAuth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  handleLoginModal: () => void;
  requireAuth?: boolean; // Optional prop to make route protection conditional
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  handleLoginModal,
  requireAuth = true,
}) => {
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    // If route requires authentication and user is not authenticated, show login modal
    if (requireAuth && !isAuthenticated) {
      handleLoginModal();
    }
  }, [isAuthenticated, requireAuth, handleLoginModal]);

  // If route requires authentication and user is not authenticated, return null
  // The modal will be shown by the useEffect above
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // Render children if authenticated or if authentication is not required
  return <>{children}</>;
};

export default ProtectedRoute;
