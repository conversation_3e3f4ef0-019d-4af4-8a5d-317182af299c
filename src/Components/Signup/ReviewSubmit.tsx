import React, { useState } from "react";
import { PROFILE_TYPES } from "../../shared/enum";
import { PersonalFormValues, FormValues } from "../../shared/interface";

interface ReviewSubmitProps {
  personalData: PersonalFormValues;
  companyData?: FormValues;
  profileType: PROFILE_TYPES | null;
  onSubmit: (referenceCode?: string) => void;
  onPrev: () => void;
  isSubmitting: boolean;
}

const ReviewSubmit: React.FC<ReviewSubmitProps> = ({
  personalData,
  companyData,
  profileType,
  onSubmit,
  onPrev,
  isSubmitting,
}) => {
  const [referenceCode, setReferenceCode] = useState("");

  const getUserTypeDisplay = (type: PROFILE_TYPES | null) => {
    switch (type) {
      case PROFILE_TYPES.DISPLAYER:
        return "Technology Displayer";
      case PROFILE_TYPES.SCOUTER:
        return "Technology Scouter";
      case PROFILE_TYPES.GENERAL_SUBSCRIBER:
        return "General Subscriber";
      default:
        return "Unknown";
    }
  };

  const handleSubmit = () => {
    onSubmit(referenceCode);
  };

  return (
    <div className="review-submit">
      <div className="review-header">
        <div className="header-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
          </svg>
        </div>
        <h1 className="review-title">Review Your Information</h1>
        <p className="review-subtitle">
          Please review your details before submitting your registration
        </p>
      </div>

      <div className="review-content">
        {/* User Type Section */}
        <div className="review-section">
          <h3 className="section-title">Account Type</h3>
          <div className="review-card">
            <div className="review-item">
              <span className="item-label">User Type:</span>
              <span className="item-value">
                {getUserTypeDisplay(profileType)}
              </span>
            </div>
          </div>
        </div>

        {/* Personal Information Section */}
        <div className="review-section">
          <h3 className="section-title">Personal Information</h3>
          <div className="review-card">
            <div className="review-item">
              <span className="item-label">Full Name:</span>
              <span className="item-value">{personalData.name}</span>
            </div>
            <div className="review-item">
              <span className="item-label">Email:</span>
              <span className="item-value">{personalData.email}</span>
            </div>
            <div className="review-item">
              <span className="item-label">Phone:</span>
              <span className="item-value">{personalData.phone}</span>
            </div>
          </div>
        </div>

        {/* Company Information Section */}
        {(profileType === PROFILE_TYPES.DISPLAYER ||
          profileType === PROFILE_TYPES.SCOUTER) &&
          companyData && (
            <div className="review-section">
              <h3 className="section-title">Company Information</h3>
              <div className="review-card">
                <div className="review-item">
                  <span className="item-label">Company Name:</span>
                  <span className="item-value">{companyData.name}</span>
                </div>
                <div className="review-item">
                  <span className="item-label">Description:</span>
                  <span className="item-value">{companyData.description}</span>
                </div>
                <div className="review-item">
                  <span className="item-label">Website:</span>
                  <span className="item-value">{companyData.website}</span>
                </div>
                <div className="review-item">
                  <span className="item-label">Country:</span>
                  <span className="item-value">{companyData.country}</span>
                </div>
                <div className="review-item">
                  <span className="item-label">Turnover:</span>
                  <span className="item-value">{companyData.turnover}</span>
                </div>
              </div>
            </div>
          )}

        {/* Reference Code Section */}
        <div className="review-section">
          <h3 className="section-title">Reference Code (Optional)</h3>
          <div className="review-card">
            <div className="reference-input-group">
              <label htmlFor="referenceCode" className="reference-label">
                Do you have a reference code?
              </label>
              <div className="reference-input-wrapper">
                <div className="reference-input-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9M10,16V19.08L13.08,16H20V4H4V16H10Z" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="referenceCode"
                  name="referenceCode"
                  value={referenceCode}
                  onChange={(e) => setReferenceCode(e.target.value)}
                  className="reference-input"
                  placeholder="Enter referral code if you have one"
                />
              </div>
              <div className="reference-helper">
                <span className="reference-helper-text">
                  If someone referred you to GTI, enter their code here
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="review-section">
          <div className="terms-card">
            <div className="terms-content">
              <h4 className="terms-title">Terms and Conditions</h4>
              <p className="terms-text">
                By submitting this registration, you agree to our Terms of
                Service and Privacy Policy. You confirm that all information
                provided is accurate and complete.
              </p>
              <div className="terms-links">
                <a href="/terms" className="terms-link">
                  Terms of Service
                </a>
                <a href="/privacy" className="terms-link">
                  Privacy Policy
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="review-actions">
        <button
          type="button"
          onClick={onPrev}
          className="back-button"
          disabled={isSubmitting}
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
          </svg>
          <span>Back</span>
        </button>

        <button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="submit-button"
        >
          {isSubmitting ? (
            <>
              <div className="spinner"></div>
              <span>Creating Account...</span>
            </>
          ) : (
            <>
              <span>Create Account</span>
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
              </svg>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ReviewSubmit;
