/* Modern SignIn Modal Styles */

/* Overlay */
.modern-signin-overlay {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Main Container */
.modern-signin-container,
.modern-forgot-container {
  @apply relative w-full max-w-md mx-auto;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Signup Animations */
@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(10px, 10px);
  }
}

@keyframes containerFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-5px) scale(1.002);
  }
}

@keyframes headerSlideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressGlow {
  0%,
  100% {
    opacity: 0.6;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.02);
  }
}

@keyframes stepFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes stepPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes stepComplete {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes numberGlow {
  0%,
  100% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2),
      0 8px 25px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.3),
      0 12px 35px rgba(59, 130, 246, 0.4);
  }
}

@keyframes progressFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes contentSlideOut {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(-30px) scale(0.98);
  }
}

@keyframes buttonPulse {
  0%,
  100% {
    box-shadow: 0 12px 30px rgba(21, 30, 112, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }
  50% {
    box-shadow: 0 15px 40px rgba(21, 30, 112, 0.5),
      0 0 0 2px rgba(255, 255, 255, 0.15) inset;
  }
}

@keyframes buttonShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes cardHover {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-8px) scale(1.02);
  }
  100% {
    transform: translateY(-12px) scale(1.03);
  }
}

@keyframes mobileContainerSlide {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes mobileHeaderSlide {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes inputFocus {
  0% {
    transform: translateY(0) scale(1);
    box-shadow: 0 0 0 0px rgba(59, 130, 246, 0);
  }
  50% {
    transform: translateY(-1px) scale(1.005);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  100% {
    transform: translateY(-3px) scale(1.01);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15),
      0 12px 35px rgba(59, 130, 246, 0.2),
      0 0 0 1px rgba(59, 130, 246, 0.3) inset;
  }
}

@keyframes buttonLoading {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes loadingShimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Close Button */
.modern-close-button {
  @apply absolute top-4 right-4 z-10 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #64748b;
}

.modern-close-button:hover {
  @apply bg-red-50 text-red-600 border-red-200;
  transform: scale(1.05);
}

.modern-close-button svg {
  @apply w-5 h-5;
}

/* Header */
.modern-signin-header {
  @apply p-8 pb-4 text-center;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(147, 51, 234, 0.05)
  );
}

.signin-logo-section {
  @apply space-y-4;
}

.signin-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.signin-icon.forgot-icon {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.signin-icon svg {
  @apply w-8 h-8;
}

.signin-title {
  @apply text-2xl font-bold text-gray-800;
  font-family: "Inter", sans-serif;
}

.signin-subtitle {
  @apply text-gray-600;
  font-family: "Inter", sans-serif;
}

/* Form */
.modern-signin-form {
  @apply p-8 pt-4 space-y-6;
}

/* Enhanced Input Groups */
.enhanced-input-group {
  @apply space-y-2;
  animation: slideInUp 0.4s ease-out;
}

.enhanced-input-group:nth-child(2) {
  animation-delay: 0.1s;
}

.enhanced-input-group:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-label {
  @apply block text-sm font-semibold text-gray-700 mb-2;
  font-family: "Inter", sans-serif;
}

.input-wrapper {
  @apply relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  height: 56px;
  display: flex;
  align-items: center;
}

.input-wrapper:focus-within {
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15),
    0 12px 35px rgba(59, 130, 246, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.3) inset;
  transform: translateY(-3px) scale(1.01);
  animation: inputFocus 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper:hover:not(:focus-within) {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.95);
}

.input-icon {
  @apply flex-shrink-0 w-5 h-5 text-gray-400 transition-all duration-300;
  margin-left: 16px;
  margin-right: 12px;
  pointer-events: none;
}

.input-wrapper:focus-within .input-icon {
  @apply text-blue-600;
  transform: scale(1.1);
}

.enhanced-input {
  @apply flex-1 bg-transparent border-none outline-none text-gray-900 placeholder-gray-400;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  padding: 0;
  height: 100%;
  line-height: 1.5;
}

.enhanced-input::placeholder {
  @apply text-gray-400;
  font-style: italic;
}

.password-toggle {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-all duration-300 rounded-lg hover:bg-gray-100;
  margin-right: 12px;
  margin-left: 8px;
}

.password-toggle svg {
  @apply w-5 h-5;
}

.input-helper {
  @apply mt-2;
}

.helper-text {
  @apply text-xs text-gray-500;
  font-family: "Inter", sans-serif;
}

/* Input Validation States */
.input-wrapper.success {
  border-color: #151e70;
  background: rgba(21, 30, 112, 0.05);
}

.input-wrapper.success .input-icon {
  @apply text-blue-600;
}

.input-wrapper.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  animation: shake 0.5s ease-in-out;
}

.input-wrapper.error .input-icon {
  @apply text-red-600;
}

.helper-text.success {
  @apply text-blue-600;
}

.helper-text.error {
  @apply text-red-600 font-medium;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  margin-top: 0.5rem;
  animation: slideInError 0.3s ease-out;
}

.helper-text.error::before {
  content: "⚠";
  color: #ef4444;
  font-size: 0.875rem;
  flex-shrink: 0;
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Form Styling */
.modern-signin-form {
  @apply p-8 pt-4 space-y-8;
}

/* Better spacing for mobile */
@media (max-width: 640px) {
  .enhanced-input-group {
    @apply space-y-1;
  }

  .input-wrapper {
    border-radius: 12px;
    height: 52px;
  }

  .enhanced-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .input-icon {
    @apply w-4 h-4;
    margin-left: 12px;
    margin-right: 10px;
  }

  .password-toggle {
    @apply w-7 h-7;
    margin-right: 10px;
    margin-left: 6px;
  }
}

/* Forgot Password Section */
.forgot-password-section {
  @apply text-center;
}

.forgot-password-link {
  @apply text-sm text-blue-600 hover:text-blue-700 font-medium transition-all duration-300 hover:underline;
  font-family: "Inter", sans-serif;
}

.forgot-password-link:hover {
  transform: translateY(-1px);
}

/* Submit Button */
.modern-submit-button {
  @apply w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 flex items-center justify-center space-x-2;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: none;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  font-family: "Inter", sans-serif;
}

.modern-submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.modern-submit-button:actives:not(:disabled) {
  transform: translateY(0);
}

.modern-submit-button:disabled {
  @apply opacity-70 cursor-not-allowed;
  transform: none;
}

.modern-submit-button svg {
  @apply w-5 h-5;
}

/* Error Message */
.error-message {
  @apply flex items-center space-x-3 p-4 rounded-xl;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.error-message svg {
  @apply w-5 h-5 flex-shrink-0;
}

.error-message span {
  @apply text-sm font-medium;
  font-family: "Inter", sans-serif;
}

/* Success Message */
.success-message {
  @apply flex items-center space-x-3 p-4 rounded-xl;
  background: rgba(21, 30, 112, 0.1);
  border: 1px solid rgba(21, 30, 112, 0.2);
  color: #151e70;
}

.success-message svg {
  @apply w-5 h-5 flex-shrink-0;
}

.success-message span {
  @apply text-sm font-medium;
  font-family: "Inter", sans-serif;
}

/* Sign Up Section */
.signup-section {
  @apply text-center pt-4 border-t border-gray-100;
}

.signup-text {
  @apply text-gray-600 text-sm;
  font-family: "Inter", sans-serif;
}

.signup-link {
  @apply ml-2 text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-300;
  font-family: "Inter", sans-serif;
}

.signup-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modern-signin-container,
  .modern-forgot-container {
    @apply mx-4;
    max-width: calc(100vw - 32px);
  }

  .modern-signin-header {
    @apply p-6 pb-3;
  }

  .modern-signin-form {
    @apply p-6 pt-3 space-y-5;
  }

  .signin-icon {
    @apply w-14 h-14;
  }

  .signin-icon svg {
    @apply w-7 h-7;
  }

  .signin-title {
    @apply text-xl;
  }

  .signin-subtitle {
    @apply text-sm;
  }

  .modern-input {
    @apply py-3;
  }

  .modern-submit-button {
    @apply py-3;
  }
}

/* Animation for form validation */
.modern-input.error {
  @apply border-red-500;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Loading spinner integration */
.modern-submit-button .spinner {
  @apply w-5 h-5;
}

/* Focus trap for accessibility */
.modern-signin-overlay:focus-within {
  outline: none;
}

/* Smooth transitions for all interactives elements */
* {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Modern Signup Page */
.modern-signup-overlay {
  @apply min-h-screen w-full relative overflow-hidden flex items-center justify-center;
  background: linear-gradient(
    135deg,
    #0f172a 0%,
    #1e293b 25%,
    #334155 50%,
    #475569 75%,
    #64748b 100%
  );
  animation: gradientShift 8s ease-in-out infinite;
  padding: 20px;
}

.modern-signup-overlay::before {
  content: "";
  @apply absolute inset-0 opacity-20;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: gridMove 20s linear infinite;
}

/* Modern Signup Container */
.modern-signup-container {
  @apply relative w-full max-w-7xl;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(241, 245, 249, 0.95) 100%
  );
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset;
  border-radius: 24px;
  animation: containerFloat 6s ease-in-out infinite;
  max-height: 95vh;
  overflow-y: auto;
}

/* Progress Header - Modern Compact Design */
.signup-progress-header {
  @apply px-6 pt-8 pb-4;
  background: linear-gradient(
    135deg,
    rgba(248, 250, 252, 0.95),
    rgba(241, 245, 249, 0.98)
  );
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.progress-steps {
  @apply flex items-start justify-center mb-4 overflow-x-auto;
  gap: 0;
  padding: 0 2rem;
  position: relative;
}

.progress-step {
  @apply flex items-center min-w-0 relative;
  flex: 1;
  max-width: 180px;
  flex-direction: column;
  padding: 0.75rem 0.5rem;
  align-items: center;
}

/* Connection lines between steps - Better positioning */
.progress-step:not(:last-child)::after {
  content: "";
  @apply absolute h-0.5 bg-gray-300 transition-all duration-500;
  z-index: 1;
  top: 16px; /* Align with center of step circle (32px height / 2) */
  transform: translateY(-50%);
  left: calc(50% + 16px); /* Start from edge of circle */
  right: calc(-50% + 16px); /* End at edge of next circle */
  width: calc(100% - 32px); /* Account for circle widths */
}

.progress-step.completed:not(:last-child)::after {
  background-color: #151e70;
  box-shadow: 0 0 6px rgba(21, 30, 112, 0.4);
  height: 2px;
}

.progress-step.actives:not(:last-child)::after {
  background: linear-gradient(90deg, #151e70 0%, #6b7280 100%);
  height: 2px;
}

/* Step Circle Styles - Enhanced */
.progress-step.completed .step-circle {
  @apply text-white border-2;
  border-color: #151e70;
  background: linear-gradient(135deg, #151e70, #0f1654);
  box-shadow: 0 4px 16px rgba(21, 30, 112, 0.3),
    0 0 0 4px rgba(21, 30, 112, 0.1);
  z-index: 3;
  position: relative;
}

.progress-step.completed .step-title {
  @apply text-blue-700 font-bold;
}

.progress-step.completed .step-subtitle {
  @apply text-blue-600 font-medium;
}

.progress-step.actives .step-circle {
  @apply text-gray-700 border-2;
  background: white !important;
  border-color: #151e70 !important;
  box-shadow: 0 6px 20px rgba(21, 30, 112, 0.3),
    0 0 0 4px rgba(21, 30, 112, 0.1);
  transform: scale(1.1);
  z-index: 3;
  position: relative;
  animation: pulse-step 3s ease-in-out infinite;
}

.progress-step.actives .step-title {
  @apply text-blue-700 font-bold;
  font-size: 0.8rem;
}

.progress-step.actives .step-subtitle {
  @apply text-blue-600 font-semibold;
  font-size: 0.7rem;
}

.progress-step.upcoming .step-circle {
  @apply bg-white text-gray-500 border-2 border-gray-300;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 2;
  position: relative;
}

.progress-step.upcoming .step-title {
  @apply text-gray-500 font-medium;
}

.progress-step.upcoming .step-subtitle {
  @apply text-gray-400;
}

.step-circle {
  @apply w-8 h-8 rounded-full flex items-center justify-center font-bold text-xs transition-all duration-300;
  min-width: 32px;
  max-width: 32px;
  height: 32px;
  font-family: "Inter", sans-serif;
  border-width: 2px;
}

.step-circle svg {
  @apply w-4 h-4;
  flex-shrink: 0;
}

.step-info {
  @apply min-w-0 flex-1 text-center mt-3;
  max-width: 140px;
}

.step-title {
  @apply font-semibold text-gray-800 leading-tight;
  font-family: "Inter", sans-serif;
  font-size: 0.75rem;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.step-subtitle {
  @apply text-gray-500 leading-tight;
  font-family: "Inter", sans-serif;
  font-size: 0.65rem;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 400;
}

.progress-bar {
  @apply w-full h-1.5 bg-gray-200 rounded-full overflow-hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.progress-fill {
  @apply h-full transition-all duration-700 ease-out;
  background: linear-gradient(90deg, #151e70 0%, #0f1654 50%, #0a0f3d 100%);
  box-shadow: 0 1px 4px rgba(21, 30, 112, 0.2);
  border-radius: inherit;
}

/* Enhanced animations and hover effects */
@keyframes pulse-step {
  0%,
  100% {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(21, 30, 112, 0.3),
      0 0 0 4px rgba(21, 30, 112, 0.1);
  }
  50% {
    transform: scale(1.12);
    box-shadow: 0 8px 25px rgba(21, 30, 112, 0.4),
      0 0 0 6px rgba(21, 30, 112, 0.15);
  }
}

@keyframes step-entrance {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation is applied via the main .progress-step.actives .step-circle rule above */

.progress-step {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: step-entrance 0.6s ease-out;
}

.progress-step:hover .step-circle {
  transform: scale(1.05);
}

.progress-step.completed:hover .step-circle {
  box-shadow: 0 6px 20px rgba(21, 30, 112, 0.4),
    0 0 0 6px rgba(21, 30, 112, 0.15);
}

.progress-step.actives:hover .step-circle {
  animation-play-state: paused;
  transform: scale(1.15);
  box-shadow: 0 8px 25px rgba(21, 30, 112, 0.4),
    0 0 0 6px rgba(21, 30, 112, 0.2);
}

.progress-step.upcoming:hover .step-circle {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Step Content - Enhanced */
.signup-step-content {
  @apply p-8 relative;
  padding-top: 2rem;
  padding-bottom: 4rem;
  animation: contentSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(248, 250, 252, 0.05) 100%
  );
  min-height: auto;
}

.signup-step-content::before {
  content: "";
  @apply absolute inset-0 opacity-5;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(139, 92, 246, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.step-wrapper {
  @apply w-full max-w-4xl mx-auto;
}

/* User Type Selection - Modern Design */
.user-type-selection {
  @apply space-y-12 max-w-6xl mx-auto;
}

.selection-header {
  @apply text-center space-y-6 mb-12;
}

.header-icon {
  @apply w-20 h-20 mx-auto rounded-3xl flex items-center justify-center mb-6;
  background: linear-gradient(135deg, #151e70, #0f1654);
  color: white;
  box-shadow: 0 20px 40px rgba(21, 30, 112, 0.3);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 20px 40px rgba(21, 30, 112, 0.3);
  }
  100% {
    box-shadow: 0 25px 50px rgba(21, 30, 112, 0.4);
  }
}

.header-icon svg {
  @apply w-10 h-10;
}

.selection-title {
  @apply text-4xl font-bold text-gray-900 mb-4;
  font-family: "Plus Jakarta Sans", sans-serif;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.selection-subtitle {
  @apply text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.user-type-grid {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-8;
}

.user-type-card {
  @apply relative bg-white rounded-3xl border border-gray-200 p-6 cursor-pointer transition-all duration-700 overflow-hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  animation: cardFadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-type-card::before {
  content: "";
  @apply absolute inset-0 opacity-0 transition-opacity duration-500;
  background: linear-gradient(
    135deg,
    rgba(21, 30, 112, 0.02) 0%,
    rgba(59, 130, 246, 0.05) 50%,
    rgba(139, 92, 246, 0.02) 100%
  );
  pointer-events: none;
}

.user-type-card:hover {
  border-color: rgba(21, 30, 112, 0.4);
  box-shadow: 0 25px 60px rgba(21, 30, 112, 0.15),
    0 0 0 1px rgba(21, 30, 112, 0.1) inset;
  transform: translateY(-12px) scale(1.03);
  animation: cardHover 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-type-card:hover::before {
  opacity: 1;
}

.user-type-card:hover .card-description {
  @apply opacity-100;
  max-height: 200px;
  margin-bottom: 1rem;
}

.user-type-card:hover .card-features {
  @apply opacity-100;
  max-height: 250px;
}

.user-type-card.selected {
  @apply border-2;
  border-color: #151e70;
  box-shadow: 0 30px 70px rgba(21, 30, 112, 0.25);
  background: linear-gradient(
    135deg,
    rgba(21, 30, 112, 0.05) 0%,
    rgba(15, 22, 84, 0.08) 100%
  );
  transform: translateY(-8px) scale(1.02);
}

.user-type-card.selected .card-description {
  @apply opacity-100;
  max-height: 200px;
  margin-bottom: 1rem;
}

.user-type-card.selected .card-features {
  @apply opacity-100;
  max-height: 250px;
}

.card-gradient {
  @apply w-20 h-20 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.user-type-card:hover .card-gradient {
  transform: scale(1.1);
}

.card-icon {
  @apply text-4xl transition-transform duration-300;
}

.user-type-card:hover .card-icon {
  transform: rotate(12deg);
}

.card-content {
  @apply space-y-4;
}

.card-title {
  @apply text-2xl font-bold text-gray-900 mb-1;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.card-subtitle {
  @apply text-blue-600 font-semibold text-lg mb-2;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.card-description {
  @apply text-gray-600 leading-relaxed mb-4 opacity-0 max-h-0 overflow-hidden transition-all duration-300;
  font-family: "DM Sans", sans-serif;
  font-size: 15px;
  line-height: 1.6;
}

.card-features {
  @apply space-y-2 opacity-0 max-h-0 overflow-hidden transition-all duration-300;
}

.feature-item {
  @apply flex items-start space-x-3 text-gray-700;
  font-family: "DM Sans", sans-serif;
  font-size: 14px;
}

.feature-item svg {
  @apply w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5;
}

.card-selector {
  @apply absolute top-6 right-6;
}

.selector-circle {
  @apply w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center transition-all duration-300;
  background: white;
}

.selector-circle.selected {
  @apply text-white;
  border-color: #151e70;
  background: linear-gradient(135deg, #151e70, #0f1654);
  box-shadow: 0 6px 20px rgba(21, 30, 112, 0.4);
  transform: scale(1.1);
}

.selector-circle svg {
  @apply w-5 h-5;
}

.selection-footer {
  @apply text-center space-y-6 mt-12;
}

.continue-button {
  @apply inline-flex items-center space-x-3 px-12 py-4 rounded-2xl font-bold text-white transition-all duration-500 text-lg relative overflow-hidden;
  background: linear-gradient(135deg, #151e70, #0f1654);
  box-shadow: 0 12px 30px rgba(21, 30, 112, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  font-family: "Plus Jakarta Sans", sans-serif;
  animation: buttonPulse 3s ease-in-out infinite;
}

.continue-button::before {
  content: "";
  @apply absolute inset-0 opacity-0 transition-opacity duration-500;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  animation: buttonShimmer 3s ease-in-out infinite;
}

.continue-button:hover:not(:disabled) {
  transform: translateY(-4px) scale(1.08);
  box-shadow: 0 25px 60px rgba(21, 30, 112, 0.6),
    0 0 0 2px rgba(255, 255, 255, 0.2) inset;
  background: linear-gradient(135deg, #0a0f3d, #060a2a);
}

.continue-button:hover:not(:disabled)::before {
  opacity: 1;
  transform: translateX(100%);
}

.continue-button:active:not(:disabled) {
  transform: translateY(-2px) scale(1.05);
  transition-duration: 0.1s;
}

.continue-button:disabled {
  @apply opacity-50 cursor-not-allowed;
  transform: none;
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  animation: none;
}

.continue-button:disabled::before {
  display: none;
}

.continue-button svg {
  @apply w-6 h-6 transition-transform duration-300;
}

.continue-button:hover:not(:disabled) svg {
  transform: translateX(2px);
}

.continue-button.loading {
  @apply pointer-events-none;
  animation: buttonLoading 1.5s ease-in-out infinite;
}

.continue-button.loading::after {
  content: "";
  @apply absolute inset-0 rounded-2xl;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  animation: loadingShimmer 1.5s ease-in-out infinite;
}

.selection-hint {
  @apply text-gray-500 text-base;
  font-family: "DM Sans", sans-serif;
}

/* Enhanced Personal Information */
.enhanced-personal-info {
  @apply space-y-8;
}

.personal-info-header {
  @apply text-center space-y-4 mb-8;
}

.personal-info-header .header-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.personal-info-header .header-icon svg {
  @apply w-8 h-8;
}

.info-title {
  @apply text-3xl font-bold text-gray-800;
  font-family: "Inter", sans-serif;
}

.info-subtitle {
  @apply text-lg text-gray-600 max-w-2xl mx-auto;
  font-family: "Inter", sans-serif;
}

.personal-info-form {
  @apply max-w-2xl mx-auto;
}

.form-grid {
  @apply space-y-6;
}

/* Phone Input Wrapper */
.phone-input-wrapper {
  @apply flex space-x-3 items-center;
}

.country-selector {
  @apply flex-shrink-0;
  width: 80px;
  height: 56px; /* Match input wrapper height */
  display: flex;
  align-items: center;
}

/* Target the ReactFlagsSelect component directly */
.country-selector > div {
  height: 56px !important;
  width: 100% !important;
  border-radius: 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.8) !important;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  ) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.country-selector > div:focus,
.country-selector > div:hover {
  border-color: #3b82f6 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1),
    0 8px 25px rgba(59, 130, 246, 0.15) !important;
  transform: translateY(-2px);
}

/* Target the flag select button specifically */
.country-selector button {
  height: 56px !important;
  width: 100% !important;
  border-radius: 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.8) !important;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  ) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 12px !important;
}

.country-selector button:focus,
.country-selector button:hover {
  border-color: #3b82f6 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1),
    0 8px 25px rgba(59, 130, 246, 0.15) !important;
  transform: translateY(-2px);
}

/* Ensure flag image has proper sizing */
.country-selector img {
  width: 18px !important;
  height: 18px !important;
  object-fit: cover !important;
  border-radius: 2px !important;
}

/* Additional ReactFlagsSelect overrides */
.country-selector .flag-select,
.country-selector .flag-select__btn,
.country-selector .flag-select__option {
  height: 56px !important;
  min-height: 56px !important;
  max-height: 56px !important;
  border-radius: 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.8) !important;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  ) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}

.country-selector .flag-select:focus,
.country-selector .flag-select:hover,
.country-selector .flag-select__btn:focus,
.country-selector .flag-select__btn:hover {
  border-color: #3b82f6 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1),
    0 8px 25px rgba(59, 130, 246, 0.15) !important;
  transform: translateY(-2px);
}

/* Ensure dropdown positioning */
.country-selector .flag-select__options {
  top: 60px !important;
  border-radius: 12px !important;
  border: 2px solid rgba(226, 232, 240, 0.8) !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15) !important;
}

.phone-input {
  @apply flex-1;
}

.optional-text {
  @apply text-gray-400 font-normal text-sm;
}

/* Form Actions */
.form-actions {
  @apply mt-8 text-center;
}

/* Error state for helper text */
.helper-text.error {
  @apply text-red-600;
}

/* Review Submit Component */
.review-submit {
  @apply space-y-8;
}

.review-header {
  @apply text-center space-y-4 mb-8;
}

.review-header .header-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #151e70, #0f1654);
  color: white;
  box-shadow: 0 8px 25px rgba(21, 30, 112, 0.3);
}

.review-header .header-icon svg {
  @apply w-8 h-8;
}

/* Reference Code Input in Review Section */
.reference-input-group {
  @apply space-y-3;
}

.reference-label {
  @apply block text-sm font-semibold text-gray-700;
  font-family: "Inter", sans-serif;
}

.reference-input-wrapper {
  @apply relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  height: 56px;
  display: flex;
  align-items: center;
}

.reference-input-wrapper:focus-within {
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1),
    0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.reference-input-icon {
  @apply flex-shrink-0 w-5 h-5 text-gray-400 transition-all duration-300;
  margin-left: 16px;
  margin-right: 12px;
  pointer-events: none;
}

.reference-input-wrapper:focus-within .reference-input-icon {
  @apply text-blue-600;
  transform: scale(1.1);
}

.reference-input {
  @apply flex-1 bg-transparent border-none outline-none text-gray-900 placeholder-gray-400;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  padding: 0;
  height: 100%;
  line-height: 1.5;
}

.reference-input::placeholder {
  @apply text-gray-400;
  font-style: italic;
}

.reference-helper {
  @apply mt-2;
}

.reference-helper-text {
  @apply text-xs text-gray-500;
  font-family: "Inter", sans-serif;
}

/* Enhanced Company Information */
.enhanced-company-info {
  @apply space-y-8;
}

.company-info-header {
  @apply text-center space-y-4 mb-8;
}

.company-info-header .header-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
}

.company-info-header .header-icon svg {
  @apply w-8 h-8;
}

.company-info-form {
  @apply max-w-2xl mx-auto;
}

/* Textarea Wrapper */
.textarea-wrapper {
  @apply relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-height: 120px;
  display: flex;
  align-items: flex-start;
  padding: 16px;
}

.textarea-wrapper:focus-within {
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1),
    0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.textarea-wrapper:hover:not(:focus-within) {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.95);
}

.textarea-wrapper .input-icon {
  @apply flex-shrink-0 w-5 h-5 text-gray-400 transition-all duration-300;
  margin-right: 12px;
  margin-top: 2px;
  pointer-events: none;
  position: relative;
  z-index: 1;
}

.textarea-wrapper:focus-within .input-icon {
  @apply text-blue-600;
  transform: scale(1.1);
}

.enhanced-textarea {
  @apply flex-1 bg-transparent border-none outline-none text-gray-900 placeholder-gray-400 resize-none;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  padding: 0;
  line-height: 1.5;
  min-height: 88px;
  width: 100%;
}

.enhanced-textarea::placeholder {
  @apply text-gray-400;
  font-style: italic;
}

/* Enhanced Select */
.enhanced-select {
  @apply flex-1 bg-transparent border-none outline-none text-gray-900;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  padding: 0;
  height: 100%;
  line-height: 1.5;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
}

.enhanced-select option {
  @apply text-gray-900 bg-white;
  font-family: "Inter", sans-serif;
}

/* File Upload Styles */
.file-upload-area {
  @apply w-full;
}

.file-upload-label {
  @apply block w-full cursor-pointer;
}

.upload-content {
  @apply flex flex-col items-center justify-center p-8 border-2 border-dashed border-gray-300 rounded-2xl transition-all duration-300;
  background: linear-gradient(
    135deg,
    rgba(248, 250, 252, 0.8),
    rgba(241, 245, 249, 0.9)
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.upload-content:hover {
  border-color: #3b82f6;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(99, 102, 241, 0.05)
  );
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.upload-icon {
  @apply w-12 h-12 text-gray-400 mb-4 transition-all duration-300;
}

.upload-content:hover .upload-icon {
  @apply text-blue-600;
  transform: scale(1.1);
}

.upload-icon svg {
  @apply w-full h-full;
}

.upload-text {
  @apply text-center space-y-1;
}

.upload-primary {
  @apply text-sm text-gray-600 font-medium;
  font-family: "Inter", sans-serif;
}

.upload-highlight {
  @apply text-blue-600 font-semibold;
}

.upload-secondary {
  @apply text-xs text-gray-500;
  font-family: "Inter", sans-serif;
}

/* Uploaded File Display */
.uploaded-file-display {
  @apply flex items-center justify-between p-4 border border-gray-200 rounded-2xl;
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.05),
    rgba(22, 163, 74, 0.05)
  );
  border-color: rgba(34, 197, 94, 0.2);
}

.file-info {
  @apply flex items-center space-x-3 flex-1;
}

.file-icon {
  @apply w-10 h-10 text-green-600 flex-shrink-0;
}

.file-icon svg {
  @apply w-full h-full;
}

.file-details {
  @apply flex flex-col;
}

.file-name {
  @apply text-sm font-medium text-gray-900;
  font-family: "Inter", sans-serif;
}

.file-size {
  @apply text-xs text-gray-500;
  font-family: "Inter", sans-serif;
}

.remove-file-btn {
  @apply w-8 h-8 flex items-center justify-center text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-300;
}

.remove-file-btn:hover {
  transform: scale(1.1);
}

.remove-file-btn svg {
  @apply w-4 h-4;
}

/* Enhanced Technology Details */
.enhanced-technology-details {
  @apply space-y-8;
}

.technology-details-header {
  @apply text-center space-y-4 mb-8;
}

.technology-details-header .header-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #7c3aed, #5b21b6);
  color: white;
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
}

.technology-details-header .header-icon svg {
  @apply w-8 h-8;
}

.technology-details-form {
  @apply max-w-4xl mx-auto;
}

/* Form Sections */
.form-section {
  @apply space-y-6 mb-12;
}

.section-header {
  @apply text-center space-y-2 mb-8;
}

.section-title {
  @apply text-2xl font-bold text-gray-900;
  font-family: "Inter", sans-serif;
}

.section-subtitle {
  @apply text-gray-600 max-w-2xl mx-auto;
  font-family: "Inter", sans-serif;
}

/* Form Grid */
.form-grid {
  @apply space-y-12;
}

/* Editor Wrapper */
.editor-wrapper {
  @apply relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-height: 200px;
  overflow: hidden;
}

.editor-wrapper:focus-within {
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1),
    0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.editor-wrapper:hover:not(:focus-within) {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.95);
}

/* Checkbox Group */
.checkbox-group {
  @apply space-y-3;
}

.checkbox-item {
  @apply flex items-center space-x-3 p-3 rounded-xl cursor-pointer transition-all duration-300;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  border: 2px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.checkbox-item:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.checkbox-input {
  @apply w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkbox-input:checked {
  @apply bg-blue-600 border-blue-600;
}

.checkbox-label {
  @apply text-gray-700 font-medium;
  font-family: "Inter", sans-serif;
}

.checkbox-item:has(.checkbox-input:checked) {
  border-color: #3b82f6;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(99, 102, 241, 0.05)
  );
}

/* Optional Text */
.optional-text {
  @apply text-gray-500 font-normal;
  font-style: italic;
}

/* Uploaded Files Display */
.uploaded-files {
  @apply mt-4 space-y-3;
}

.uploaded-files-title {
  @apply text-sm font-semibold text-gray-700 mb-3;
  font-family: "Inter", sans-serif;
}

.uploaded-files-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-3;
}

.uploaded-files-list {
  @apply space-y-3;
}

.uploaded-file-item {
  @apply relative flex items-center space-x-3 p-3 rounded-xl border-2 border-gray-200 bg-white transition-all duration-300;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.uploaded-file-item:hover {
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-preview {
  @apply flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden flex items-center justify-center;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
}

.image-preview {
  @apply w-full h-full object-cover;
}

.document-preview {
  @apply text-blue-600;
}

.document-preview svg {
  @apply w-6 h-6;
}

.video-preview {
  @apply w-16 h-12 rounded-lg overflow-hidden;
}

.video-preview-player {
  @apply w-full h-full object-cover;
}

.file-info {
  @apply flex-1 min-w-0;
}

.file-name {
  @apply text-sm font-medium text-gray-900 truncate;
  font-family: "Inter", sans-serif;
}

.file-size {
  @apply text-xs text-gray-500;
  font-family: "Inter", sans-serif;
}

.video-item .file-preview {
  @apply w-16 h-12;
}

.document-item .file-preview {
  @apply bg-blue-50;
}

/* Enhanced Opportunity Details */
.enhanced-opportunity-details {
  @apply space-y-8;
}

.opportunity-details-header {
  @apply text-center space-y-4 mb-8;
}

.opportunity-details-header .header-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
}

.opportunity-details-header .header-icon svg {
  @apply w-8 h-8;
}

.opportunity-details-form {
  @apply max-w-4xl mx-auto;
}

.review-title {
  @apply text-3xl font-bold text-gray-800;
  font-family: "Inter", sans-serif;
}

.review-subtitle {
  @apply text-lg text-gray-600 max-w-2xl mx-auto;
  font-family: "Inter", sans-serif;
}

.review-content {
  @apply max-w-3xl mx-auto space-y-6;
}

.review-section {
  @apply space-y-4;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 border-b border-gray-200 pb-2;
  font-family: "Inter", sans-serif;
}

.review-card {
  @apply bg-white rounded-xl border border-gray-200 p-6 space-y-4;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.review-item {
  @apply flex justify-between items-start;
}

.item-label {
  @apply font-medium text-gray-600 min-w-0 flex-shrink-0 mr-4;
  font-family: "Inter", sans-serif;
  width: 140px;
}

.item-value {
  @apply text-gray-800 text-right break-words;
  font-family: "Inter", sans-serif;
}

.terms-card {
  @apply bg-blue-50 rounded-xl border border-blue-200 p-6;
}

.terms-title {
  @apply text-lg font-semibold text-blue-800 mb-3;
  font-family: "Inter", sans-serif;
}

.terms-text {
  @apply text-blue-700 text-sm leading-relaxed mb-4;
  font-family: "Inter", sans-serif;
}

.terms-links {
  @apply flex space-x-4;
}

.terms-link {
  @apply text-blue-600 hover:text-blue-800 text-sm font-medium underline transition-colors duration-300;
  font-family: "Inter", sans-serif;
}

.review-actions {
  @apply flex justify-between items-center max-w-3xl mx-auto pt-6;
}

.back-button {
  @apply inline-flex items-center space-x-2 px-6 py-3 rounded-xl font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-all duration-300;
  font-family: "Inter", sans-serif;
}

.back-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.back-button svg {
  @apply w-5 h-5;
}

.submit-button {
  @apply inline-flex items-center space-x-2 px-8 py-4 rounded-xl font-semibold text-white transition-all duration-300;
  background: linear-gradient(135deg, #151e70, #0f1654);
  box-shadow: 0 8px 25px rgba(21, 30, 112, 0.3);
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(21, 30, 112, 0.4);
}

.submit-button:disabled {
  @apply opacity-70 cursor-not-allowed;
  transform: none;
}

.submit-button svg {
  @apply w-5 h-5;
}

.spinner {
  @apply w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Slide Transitions */
.slide-enter {
  opacity: 0;
  transform: translateX(30px);
}

.slide-enter-actives {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.slide-exit {
  opacity: 1;
  transform: translateX(0);
}

.slide-exit-actives {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .modern-signup-overlay {
    @apply p-3;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .modern-signup-container {
    @apply w-full;
    border-radius: 20px;
    animation: mobileContainerSlide 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 90vh;
  }

  .signup-progress-header {
    @apply px-4 pt-6 pb-3;
    animation: mobileHeaderSlide 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .progress-steps {
    @apply flex-row justify-center mb-3 px-1;
    gap: 0.5rem;
  }

  .progress-step {
    @apply flex-col items-center;
    flex: 1;
    max-width: 120px;
    padding: 0.5rem 0.25rem;
  }

  /* Adjust connection lines for mobile */
  .progress-step:not(:last-child)::after {
    top: 12px; /* Align with center of mobile step circle (24px height / 2) */
    transform: translateY(-50%);
    left: calc(50% + 12px);
    right: calc(-50% + 12px);
    width: calc(100% - 24px);
    height: 1px;
  }

  .step-info {
    @apply mt-2;
    max-width: 100px;
  }

  .step-title {
    font-size: 0.65rem;
    @apply font-medium;
  }

  .step-subtitle {
    @apply hidden; /* Hide subtitles on mobile to save space */
  }

  .step-circle {
    @apply w-6 h-6 text-xs;
    min-width: 24px;
    max-width: 24px;
    height: 24px;
  }

  .step-circle svg {
    @apply w-3 h-3;
  }

  .progress-bar {
    @apply h-1;
  }

  .signup-step-content {
    @apply p-4;
  }

  .user-type-selection {
    @apply space-y-8;
  }

  .selection-header {
    @apply space-y-4 mb-8;
  }

  .header-icon {
    @apply w-16 h-16 mb-4;
  }

  .selection-title {
    @apply text-3xl;
  }

  .selection-subtitle {
    @apply text-lg;
  }

  .user-type-grid {
    @apply grid-cols-1 gap-6;
  }

  .user-type-card {
    @apply p-4;
  }

  .card-gradient {
    @apply w-16 h-16 mb-4;
  }

  .card-icon {
    @apply text-3xl;
  }

  .card-title {
    @apply text-xl;
  }

  .card-subtitle {
    @apply text-base;
  }

  .card-description {
    @apply text-sm opacity-0 max-h-0 overflow-hidden transition-all duration-300;
  }

  .user-type-card:hover .card-description,
  .user-type-card.selected .card-description {
    @apply opacity-100;
    max-height: 120px;
    margin-bottom: 0.75rem;
  }

  .user-type-card:hover .card-features,
  .user-type-card.selected .card-features {
    @apply opacity-100;
    max-height: 200px;
  }

  .continue-button {
    @apply px-8 py-3 text-base;
  }

  .selection-footer {
    @apply space-y-4 mt-8;
  }

  .phone-input-wrapper {
    @apply flex-col space-x-0 space-y-3;
  }

  .country-selector {
    @apply w-full;
    height: 52px; /* Match mobile input height */
  }

  .country-selector > div,
  .country-selector button {
    height: 52px !important;
    border-radius: 12px !important;
  }

  .input-wrapper {
    height: 52px;
    border-radius: 12px;
  }

  .review-actions {
    @apply flex-col space-y-4;
  }

  .back-button,
  .submit-button {
    @apply w-full justify-center;
  }

  .review-item {
    @apply flex-col items-start space-y-1;
  }

  .item-label {
    @apply w-full mr-0;
  }

  .item-value {
    @apply text-left;
  }

  /* Company Information Mobile Styles */
  .enhanced-company-info {
    @apply space-y-6;
  }

  .company-info-header {
    @apply mb-6;
  }

  .company-info-header .header-icon {
    @apply w-14 h-14;
  }

  .company-info-header .header-icon svg {
    @apply w-7 h-7;
  }

  .info-title {
    @apply text-2xl;
  }

  .info-subtitle {
    @apply text-base;
  }

  .textarea-wrapper {
    border-radius: 12px;
    min-height: 100px;
    padding: 12px;
  }

  .textarea-wrapper .input-icon {
    margin-right: 10px;
    margin-top: 1px;
  }

  .enhanced-textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 68px;
    padding: 0;
  }

  .upload-content {
    @apply p-6;
    border-radius: 16px;
  }

  .upload-icon {
    @apply w-10 h-10 mb-3;
  }

  .uploaded-file-display {
    @apply p-3;
    border-radius: 16px;
  }

  .file-icon {
    @apply w-8 h-8;
  }

  .remove-file-btn {
    @apply w-7 h-7;
  }

  /* Technology Details Mobile Styles */
  .enhanced-technology-details {
    @apply space-y-6;
  }

  .technology-details-header {
    @apply mb-6;
  }

  .technology-details-header .header-icon {
    @apply w-14 h-14;
  }

  .technology-details-header .header-icon svg {
    @apply w-7 h-7;
  }

  .form-section {
    @apply space-y-4 mb-8;
  }

  .section-header {
    @apply mb-6;
  }

  .section-title {
    @apply text-xl;
  }

  .section-subtitle {
    @apply text-sm;
  }

  .form-grid {
    @apply space-y-8;
  }

  .editor-wrapper {
    border-radius: 12px;
    min-height: 150px;
  }

  .checkbox-item {
    @apply p-2;
    border-radius: 12px;
  }

  .checkbox-input {
    @apply w-4 h-4;
  }

  .checkbox-label {
    @apply text-sm;
  }

  /* Opportunity Details Mobile Styles */
  .enhanced-opportunity-details {
    @apply space-y-6;
  }

  .opportunity-details-header {
    @apply mb-6;
  }

  .opportunity-details-header .header-icon {
    @apply w-14 h-14;
  }

  .opportunity-details-header .header-icon svg {
    @apply w-7 h-7;
  }

  /* Uploaded Files Mobile Styles */
  .uploaded-files-grid {
    @apply grid-cols-1;
  }

  .uploaded-file-item {
    @apply p-2;
    border-radius: 12px;
  }

  .file-preview {
    @apply w-10 h-10;
  }

  .video-item .file-preview {
    @apply w-14 h-10;
  }

  .file-name {
    @apply text-xs;
  }

  .file-size {
    @apply text-xs;
  }

  .uploaded-files-title {
    @apply text-xs;
  }
}

@media (max-width: 640px) {
  .signup-progress-header {
    @apply px-3 pt-4 pb-2;
  }

  .progress-steps {
    @apply flex justify-center mb-2 px-1;
    gap: 0.25rem;
  }

  .progress-step {
    @apply flex-col items-center;
    flex: none;
    min-width: 50px;
    padding: 0.25rem;
  }

  /* Hide connection lines on very small screens */
  .progress-step:not(:last-child)::after {
    @apply hidden;
  }

  .step-info {
    @apply mt-1;
    max-width: 50px;
  }

  .step-title {
    @apply font-medium leading-tight;
    font-size: 8px;
    max-width: 50px;
  }

  .step-subtitle {
    @apply hidden;
  }

  .step-circle {
    @apply w-5 h-5;
    min-width: 20px;
    max-width: 20px;
    height: 20px;
    font-size: 8px;
  }

  .step-circle svg {
    @apply w-2.5 h-2.5;
  }

  .progress-bar {
    @apply h-0.5;
  }

  .modern-signup-container {
    max-height: 98vh;
  }

  .signup-step-content {
    max-height: calc(98vh - 80px);
    @apply p-3;
  }
}

/* Modern Success Modal Styles */
.modern-success-overlay {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(15, 23, 42, 0.9) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-success-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modern-success-overlay.hidden {
  opacity: 0;
  visibility: hidden;
}

.modern-success-container {
  @apply relative w-full max-w-md;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  animation: successModalSlide 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.success-modal-content {
  @apply p-8 space-y-8;
}

.success-header {
  @apply text-center space-y-6;
}

.success-icon {
  @apply w-20 h-20 mx-auto rounded-full flex items-center justify-center relative;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.success-icon.loading {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  animation: iconPulse 2s ease-in-out infinite;
}

.success-icon.success {
  background: linear-gradient(135deg, #151e70, #0f1654);
  color: white;
  animation: successBounce 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.success-icon.error {
  background: linear-gradient(135deg, #151e70, #0f1654);
  color: white;
  animation: errorShake 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.success-icon svg {
  @apply w-10 h-10;
}

.loading-spinner {
  @apply w-10 h-10;
}

.loading-spinner svg {
  @apply w-full h-full;
  color: white;
}

.success-text {
  @apply space-y-3;
}

.success-title {
  @apply text-2xl font-bold text-gray-900;
  font-family: "Inter", sans-serif;
}

.success-message {
  @apply text-gray-600 leading-relaxed;
  font-family: "Inter", sans-serif;
}

.success-actions {
  @apply flex justify-center;
}

.success-button {
  @apply inline-flex items-center space-x-3 px-8 py-4 rounded-2xl font-semibold text-white transition-all duration-500 relative overflow-hidden;
  font-family: "Inter", sans-serif;
  min-width: 160px;
  justify-content: center;
}

.success-button.success {
  background: linear-gradient(135deg, #151e70, #0f1654);
  box-shadow: 0 12px 30px rgba(21, 30, 112, 0.4);
}

.success-button.success:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 20px 50px rgba(21, 30, 112, 0.5);
  background: linear-gradient(135deg, #0f1654, #0a1142);
}

.success-button.error {
  background: linear-gradient(135deg, #151e70, #0f1654);
  box-shadow: 0 12px 30px rgba(21, 30, 112, 0.4);
}

.success-button.error:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 20px 50px rgba(21, 30, 112, 0.5);
  background: linear-gradient(135deg, #0f1654, #0a1142);
}

.success-button svg {
  @apply w-5 h-5 transition-transform duration-300;
}

.success-button:hover svg {
  transform: translateX(2px);
}

/* Success Modal Animations */
@keyframes successModalSlide {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes successBounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes errorShake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .modern-success-container {
    @apply mx-4;
    max-width: calc(100vw - 32px);
    border-radius: 20px;
  }

  .success-modal-content {
    @apply p-6 space-y-6;
  }

  .success-icon {
    @apply w-16 h-16;
  }

  .success-icon svg {
    @apply w-8 h-8;
  }

  .success-title {
    @apply text-xl;
  }

  .success-message {
    @apply text-sm;
  }

  .success-button {
    @apply px-6 py-3 text-sm;
  }
}
