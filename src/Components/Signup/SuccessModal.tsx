import ReactDOM from "react-dom";
import { useNavigate } from "react-router-dom";
import loading from "../../assests/loading.webp";
import success from "../../assests/success.gif";
import error from "../../assests/error.gif";
import { useEffect, useState } from "react";

const SuccessModal = ({
  show,
  state,
  message,
  toggle,
}: {
  show: boolean;
  state: string;
  message: string;
  toggle: (isOpen: boolean, state: string, message: string) => void;
}) => {
  let navigate = useNavigate();

  const [modalDetails, setModalDetails] = useState({
    image: loading,
    message,
    buttonText: "",
  });

  useEffect(() => {
    if (state === "LOADING") {
      setModalDetails({
        image: loading,
        message,
        buttonText: "",
      });
    } else if (state === "SUCCESS") {
      setModalDetails({
        image: success,
        message,
        buttonText: "Home",
      });
    } else if (state === "ERROR") {
      setModalDetails({
        image: error,
        message,
        buttonText: "Try Again",
      });
    }
  }, [state, message]);

  const content = (
    <div
      className={`modern-success-overlay ${show ? "show" : "hidden"}`}
      aria-hidden={!show}
    >
      <div className="modern-success-container">
        <div className="success-modal-content">
          {/* Header with Icon */}
          <div className="success-header">
            <div className={`success-icon ${state.toLowerCase()}`}>
              {state === "LOADING" && (
                <div className="loading-spinner">
                  <svg viewBox="0 0 24 24" fill="none">
                    <circle
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeDasharray="31.416"
                      strokeDashoffset="31.416"
                    >
                      <animate
                        attributeName="stroke-dasharray"
                        dur="2s"
                        values="0 31.416;15.708 15.708;0 31.416;0 31.416"
                        repeatCount="indefinite"
                      />
                      <animate
                        attributeName="stroke-dashoffset"
                        dur="2s"
                        values="0;-15.708;-31.416;-31.416"
                        repeatCount="indefinite"
                      />
                    </circle>
                  </svg>
                </div>
              )}
              {state === "SUCCESS" && (
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.41,10.09L6,11.5L11,16.5Z" />
                </svg>
              )}
              {state === "ERROR" && (
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                </svg>
              )}
            </div>

            <div className="success-text">
              <h2 className="success-title">
                {state === "LOADING" && "Processing..."}
                {state === "SUCCESS" && "Account Created Successfully!"}
                {state === "ERROR" && "Registration Failed"}
              </h2>
              <p className="success-message">{modalDetails.message}</p>
            </div>
          </div>

          {/* Action Buttons */}
          {modalDetails?.buttonText && state !== "LOADING" && (
            <div className="success-actions">
              <button
                onClick={() => {
                  toggle(false, "LOADING", "");
                  if (state === "SUCCESS") {
                    navigate("/");
                  }
                  // For ERROR state, just close the modal to allow user to try again
                }}
                className={`success-button ${state.toLowerCase()}`}
              >
                <span>{modalDetails.buttonText}</span>
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
  return ReactDOM.createPortal(content, document.body);
};

export default SuccessModal;
