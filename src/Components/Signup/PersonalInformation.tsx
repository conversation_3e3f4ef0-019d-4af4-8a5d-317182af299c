import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>illEyeInvisible } from "react-icons/ai";
import ReactFlagsSelect from "react-flags-select";
import { PROFILE_TYPES } from "../../shared/enum";

const PersonalInformation = ({
  profileType,
  country,
  personalFormik,
  handleCountry,
  handleProfileTypes,
}: {
  profileType: PROFILE_TYPES;
  country: string;
  personalFormik: any;
  handleCountry: (e: string) => void;
  handleProfileTypes: (e: PROFILE_TYPES) => void;
}) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="enhanced-personal-info">
      <div className="personal-info-header">
        <div className="header-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
          </svg>
        </div>
        <h1 className="info-title">Account Setup</h1>
        <p className="info-subtitle">
          Tell us about yourself to create your GTI account
        </p>
      </div>

      <form
        onSubmit={personalFormik.handleSubmit}
        className="personal-info-form"
      >
        <div className="form-grid">
          {/* Full Name Field */}
          <div className="enhanced-input-group">
            <label htmlFor="name" className="input-label">
              Full Name
            </label>
            <div className="input-wrapper">
              <div className="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                </svg>
              </div>
              <input
                type="text"
                id="name"
                name="name"
                value={personalFormik.values.name}
                onChange={personalFormik.handleChange}
                className="enhanced-input"
                placeholder="John Doe"
                required
              />
            </div>
            {personalFormik.errors.name && personalFormik.touched.name && (
              <div className="input-helper">
                <span className="helper-text error">
                  {personalFormik.errors.name}
                </span>
              </div>
            )}
          </div>
          {/* Email Field */}
          <div className="enhanced-input-group">
            <label htmlFor="email" className="input-label">
              Email Address
            </label>
            <div className="input-wrapper">
              <div className="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                </svg>
              </div>
              <input
                type="email"
                id="email"
                name="email"
                value={personalFormik.values.email}
                onChange={personalFormik.handleChange}
                className="enhanced-input"
                placeholder="<EMAIL>"
                required
              />
            </div>
            {personalFormik.errors.email && personalFormik.touched.email && (
              <div className="input-helper">
                <span className="helper-text error">
                  {personalFormik.errors.email}
                </span>
              </div>
            )}
          </div>
          {/* Password Field */}
          <div className="enhanced-input-group">
            <label htmlFor="password" className="input-label">
              Password
            </label>
            <div className="input-wrapper">
              <div className="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18,8h-1V6c0-2.76-2.24-5-5-5S7,3.24,7,6v2H6c-1.1,0-2,0.9-2,2v10c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V10C20,8.9,19.1,8,18,8z M12,17c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,17,12,17z M15.1,8H8.9V6c0-1.71,1.39-3.1,3.1-3.1s3.1,1.39,3.1,3.1V8z" />
                </svg>
              </div>
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={personalFormik.values.password}
                onChange={personalFormik.handleChange}
                className="enhanced-input"
                placeholder="••••••••"
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label="Toggle password visibility"
              >
                {showPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
              </button>
            </div>
            {personalFormik.errors.password &&
              personalFormik.touched.password && (
                <div className="input-helper">
                  <span className="helper-text error">
                    {personalFormik.errors.password}
                  </span>
                </div>
              )}
          </div>
          {/* Phone Field */}
          <div className="enhanced-input-group">
            <label htmlFor="phone" className="input-label">
              Phone Number
            </label>
            <div className="phone-input-wrapper">
              <ReactFlagsSelect
                selected={country}
                onSelect={(code) => {
                  personalFormik.setFieldValue("country", code);
                  handleCountry(code);
                }}
                id="country"
                fullWidth={false}
                selectedSize={18}
                showSelectedLabel={false}
                showSecondarySelectedLabel={false}
                showSecondaryOptionLabel={false}
                searchable
                className="country-selector"
              />
              <div className="input-wrapper phone-input">
                <div className="input-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z" />
                  </svg>
                </div>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={personalFormik.values.phone}
                  onChange={personalFormik.handleChange}
                  className="enhanced-input"
                  placeholder="1234567890"
                  required
                />
              </div>
            </div>
            {personalFormik.errors.phone && personalFormik.touched.phone && (
              <div className="input-helper">
                <span className="helper-text error">
                  {personalFormik.errors.phone}
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="form-actions">
          <button type="submit" className="continue-button">
            <span>
              {profileType === PROFILE_TYPES.GENERAL_SUBSCRIBER
                ? "Complete Registration"
                : "Continue"}
            </span>
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default PersonalInformation;
