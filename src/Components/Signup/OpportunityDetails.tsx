import React, { Dispatch, useEffect, useRef, useState } from "react";
import { notify } from "../../utils";
import { createUserOppotunity } from "../../store/actioncreators/opportunityactions";
import {
  CONTENT_TYPE,
  CONTENT_TYPE_DOC,
  FILE_PATH,
  FILE_TYPE,
  oppotunityItem,
  presignedData,
  sectorItem,
  subsectorItem,
} from "../constants";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import axios from "axios";
import { RequestMethods } from "../../shared/RequestMethods";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { useFormik } from "formik";
import { oppSchema } from "../validations/oppValidations";
import { Editor } from "@tinymce/tinymce-react";

import createProductLogo from "../../assests/images/signup/create_product.svg";
import { getSubSectorBySector } from "../../store/actioncreators/sub-sectoractions";
import { getSectors } from "../../store/actioncreators/sectoractions";
import CustomEditor from "../shared/CustomEditor";

interface MyFormValues {
  name: string;
  tech_require: string;
  description: string;
  sectorId: string;
  subSectorId: string;
}

type files = {
  image: Boolean;
  document: Boolean;
  imageFile: File[];
  documentFiles: File[];
  videos: File[];
};

const OpportunityDetails = ({
  handleTechnologySubmit,
  handleSuccessModal,
}: {
  handleTechnologySubmit: () => Promise<string>;
  handleSuccessModal: (isOpen: boolean, state: string, message: string) => void;
}) => {
  const [sectors, setSectors] = useState<sectorItem[]>([]);

  const docInputRef = useRef<HTMLInputElement>(null);

  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );

  const initialValues: MyFormValues = {
    name: "",
    tech_require: "",
    description: "",
    sectorId: sectors.length ? sectors[0]._id : "",
    subSectorId: "",
  };

  const dispatch: Dispatch<any> = useDispatch();

  const handleFetchSectors = async () => {
    const fetchedSectors = await getSectors();
    setSectors(fetchedSectors);
  };

  useEffect(() => {
    handleFetchSectors();
  }, []);

  const [files, setFiles] = useState<files>({
    image: false,
    document: false,
    imageFile: [],
    documentFiles: [],
    videos: [],
  });

  const handleImage = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files || [];
    if (!fileList) return;

    const images = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      images.push(fileList[i]);
    }

    setFiles({ ...files, imageFile: images, image: false });
  };

  const handleDocuments = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files || [];
    if (!fileList) return;

    const documentFiles = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      documentFiles.push(fileList[i]);
    }

    setFiles({ ...files, documentFiles, document: false });
  };

  const handleVideo = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e?.target.files || [];
    if (!fileList) return;

    const videos = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      const fileSizeInMB = fileList[i].size / (1024 * 1024); // Convert file size to MB
      if (fileSizeInMB <= 50) {
        videos.push(fileList[i]);
      } else {
        alert("Please select a file smaller than 50 MB.");
      }
    }

    setFiles({ ...files, videos });
  };

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = "error";
        dispatch(failToast());
      });

    return result;
  };

  const postLogo = async (signed: string, imageFile: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: imageFile,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const postDocument = async (signed: string, file: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE_DOC,
        "Access-Control-Allow-Origin": true,
      },
      data: file,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const handleCreate = async (values: MyFormValues) => {
    try {
      handleSuccessModal(true, "LOADING", "Please Wait");

      const userId = await handleTechnologySubmit();
      if (!userId) return;

      if (!files.imageFile) {
        return setFiles({ ...files, image: true });
      }
      setFiles({ ...files, document: false, image: false });

      let opportunityImages: string[] = [];
      let documents: string[] = [];

      for (const file of files.imageFile) {
        const signedLogoData: presignedData = {
          fileName: file.name ?? values.name,
          filePath: FILE_PATH.PRODUCTS_IMAGE,
          fileType: FILE_TYPE.PNG,
        };
        let imageUrl = await getPresigned(signedLogoData);
        await postLogo(imageUrl, file);
        opportunityImages.push(imageUrl.split("?")[0]);
      }

      for (const file of files.documentFiles) {
        const signedDocumentData: presignedData = {
          fileName: file.name || values.name,
          filePath: FILE_PATH.COMPANY_DOCS,
          fileType: FILE_TYPE.PDF,
        };
        let documentUrl = await getPresigned(signedDocumentData);
        await postDocument(documentUrl, file);
        documents.push(documentUrl.split("?")[0]);
      }

      const data: oppotunityItem = {
        technologyPartnerRequirement: values.tech_require,
        description: values.description,
        documents,
        images: opportunityImages,
        sectorId: values.sectorId,
        subSectorId: values.subSectorId,
        name: values.name,
      };

      const result = await (dispatch as any)(
        createUserOppotunity(data, userId)
      );

      if (result?.success) {
        handleSuccessModal(
          true,
          "SUCCESS",
          "An email has been sent on your mail address, please verify it within 24 hours."
        );
      } else {
        handleSuccessModal(
          true,
          "ERROR",
          result?.message || "Failed to Register, Please Retry"
        );
      }
    } catch (error) {
      handleSuccessModal(true, "ERROR", "Failed to Register, Please Retry");
    }
  };

  const fetchSubsector = async (sectorId: string, currentId: string) => {
    if (currentId !== sectorId || currentId === "")
      dispatch(getSubSectorBySector(sectorId));
  };

  useEffect(() => {
    if (sectors?.length) {
      fetchSubsector(sectors[0]?._id, "");
    }
  }, [sectors]);

  const opportunityformik = useFormik<MyFormValues>({
    initialValues: initialValues,
    validationSchema: oppSchema,
    onSubmit: (values, formikHelpers) => {
      handleCreate(values);
    },
  });

  return (
    <div className="enhanced-opportunity-details">
      <div className="opportunity-details-header">
        <div className="header-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
          </svg>
        </div>
        <h1 className="info-title">Opportunity Details</h1>
        <p className="info-subtitle">
          Post your opportunity to connect with technology providers who can
          meet your requirements
        </p>
      </div>

      <form
        onSubmit={opportunityformik.handleSubmit}
        className="opportunity-details-form"
      >
        <div className="form-grid">
          {/* Basic Information Section */}
          <div className="form-section">
            <div className="section-header">
              <h2 className="section-title">Basic Information</h2>
              <p className="section-subtitle">
                Provide the essential details about your opportunity
              </p>
            </div>

            {/* Opportunity Name Field */}
            <div className="enhanced-input-group">
              <label htmlFor="name" className="input-label">
                Opportunity Name *
              </label>
              <div className="input-wrapper">
                <div className="input-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={opportunityformik.values.name}
                  onChange={(e) =>
                    opportunityformik.setFieldValue("name", e.target.value)
                  }
                  className="enhanced-input"
                  placeholder="Enter a compelling name for your opportunity"
                  required
                />
              </div>
              {opportunityformik.errors.name &&
                opportunityformik.touched.name && (
                  <div className="input-helper">
                    <span className="helper-text error">
                      {opportunityformik.errors.name}
                    </span>
                  </div>
                )}
            </div>

            {/* Technology Partner Requirement Field */}
            <div className="enhanced-input-group">
              <label htmlFor="tech_require" className="input-label">
                Technology Partner Requirement *
              </label>
              <div className="textarea-wrapper">
                <div className="input-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                </div>
                <textarea
                  id="tech_require"
                  name="tech_require"
                  value={opportunityformik.values.tech_require}
                  onChange={(e) =>
                    opportunityformik.setFieldValue(
                      "tech_require",
                      e.target.value
                    )
                  }
                  className="enhanced-textarea"
                  placeholder="Describe the specific technology or solution you're looking for..."
                  rows={4}
                  required
                />
              </div>
              {opportunityformik.errors.tech_require &&
                opportunityformik.touched.tech_require && (
                  <div className="input-helper">
                    <span className="helper-text error">
                      {opportunityformik.errors.tech_require}
                    </span>
                  </div>
                )}
              <div className="input-helper">
                <span className="helper-text">
                  Be specific about your requirements to attract the right
                  technology partners.
                </span>
              </div>
            </div>
            {/* Opportunity Description Field */}
            <div className="enhanced-input-group">
              <label htmlFor="description" className="input-label">
                Detailed Description *
              </label>
              <div className="editor-wrapper">
                <CustomEditor
                  initialContent={opportunityformik.values.description}
                  onChange={(content: string) => {
                    opportunityformik.setFieldValue("description", content);
                  }}
                />
              </div>
              {opportunityformik.errors.description &&
                opportunityformik.touched.description && (
                  <div className="input-helper">
                    <span className="helper-text error">
                      {opportunityformik.errors.description}
                    </span>
                  </div>
                )}
              <div className="input-helper">
                <span className="helper-text">
                  Provide comprehensive details about your opportunity,
                  including goals, expectations, and benefits for potential
                  partners.
                </span>
              </div>
            </div>
          </div>

          {/* Classification Section */}
          <div className="form-section">
            <div className="section-header">
              <h2 className="section-title">Classification</h2>
              <p className="section-subtitle">
                Categorize your opportunity for better discoverability
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Sector Field */}
              <div className="enhanced-input-group">
                <label htmlFor="sectorId" className="input-label">
                  Sector *
                </label>
                <div className="input-wrapper">
                  <div className="input-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
                    </svg>
                  </div>
                  <select
                    id="sectorId"
                    name="sectorId"
                    value={opportunityformik.values.sectorId}
                    onChange={(e) => {
                      fetchSubsector(
                        e.target.value,
                        opportunityformik.values.sectorId
                      );
                      opportunityformik.setFieldValue(
                        "sectorId",
                        e.target.value
                      );
                    }}
                    className="enhanced-select"
                    required
                  >
                    <option value="">Select a sector</option>
                    {sectors.map((item: sectorItem, id) => {
                      return (
                        <option key={id} value={item._id}>
                          {item.name}
                        </option>
                      );
                    })}
                  </select>
                </div>
                {opportunityformik.errors.sectorId &&
                  opportunityformik.touched.sectorId && (
                    <div className="input-helper">
                      <span className="helper-text error">
                        {opportunityformik.errors.sectorId}
                      </span>
                    </div>
                  )}
              </div>

              {/* Sub Sector Field */}
              {subsectorlist.SUB_SECTOR_LIST?.length ? (
                <div className="enhanced-input-group">
                  <label htmlFor="subSectorId" className="input-label">
                    Sub Sector *
                  </label>
                  <div className="input-wrapper">
                    <div className="input-icon">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
                      </svg>
                    </div>
                    <select
                      id="subSectorId"
                      name="subSectorId"
                      value={opportunityformik.values.subSectorId}
                      onChange={(e) =>
                        opportunityformik.setFieldValue(
                          "subSectorId",
                          e.target.value
                        )
                      }
                      className="enhanced-select"
                      required
                    >
                      <option value="">Select a sub-sector</option>
                      {subsectorlist.SUB_SECTOR_LIST.map(
                        (item: subsectorItem, id) => {
                          return (
                            <option key={id} value={item._id}>
                              {item.name}
                            </option>
                          );
                        }
                      )}
                    </select>
                  </div>
                </div>
              ) : null}
            </div>
          </div>

          {/* Media & Documents Section */}
          <div className="form-section">
            <div className="section-header">
              <h2 className="section-title">Media & Documents</h2>
              <p className="section-subtitle">
                Upload images and documents to showcase your opportunity
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Opportunity Images */}
              <div className="enhanced-input-group">
                <label htmlFor="logo" className="input-label">
                  Opportunity Images *
                </label>
                <div className="file-upload-area">
                  <label htmlFor="logo" className="file-upload-label">
                    <div className="upload-content">
                      <div className="upload-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M9,12C9,13.38 10.22,14.5 11.75,14.5C13.28,14.5 14.5,13.38 14.5,12C14.5,10.62 13.28,9.5 11.75,9.5C10.22,9.5 9,10.62 9,12M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16,16.5L11.5,12L9,14.5L8,13.5L11.5,10L16,14.5V16.5Z" />
                        </svg>
                      </div>
                      <div className="upload-text">
                        <p className="upload-primary">
                          <span className="upload-highlight">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </p>
                        <p className="upload-secondary">
                          PNG files only (Max 5MB each, up to 3 images)
                        </p>
                      </div>
                    </div>
                    <input
                      onChange={handleImage}
                      accept=".png"
                      type="file"
                      id="logo"
                      aria-label="opportunity-images"
                      className="hidden"
                      multiple
                      max={3}
                    />
                  </label>
                </div>
                {files.image && (
                  <div className="input-helper">
                    <span className="helper-text error">
                      Please upload opportunity images
                    </span>
                  </div>
                )}

                {/* Display Uploaded Images */}
                {files.imageFile && files.imageFile.length > 0 && (
                  <div className="uploaded-files">
                    <h4 className="uploaded-files-title">
                      Uploaded Images ({files.imageFile.length})
                    </h4>
                    <div className="uploaded-files-grid">
                      {files.imageFile.map((file, index) => (
                        <div key={index} className="uploaded-file-item">
                          <div className="file-preview">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Opportunity ${index + 1}`}
                              className="image-preview"
                            />
                          </div>
                          <div className="file-info">
                            <p className="file-name">{file.name}</p>
                            <p className="file-size">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newFiles = files.imageFile.filter(
                                (_, i) => i !== index
                              );
                              setFiles({ ...files, imageFile: newFiles });
                            }}
                            className="remove-file-btn"
                          >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Opportunity Documents */}
              <div className="enhanced-input-group">
                <label htmlFor="documents" className="input-label">
                  Opportunity Documents{" "}
                  <span className="optional-text">(Optional)</span>
                </label>
                <div className="file-upload-area">
                  <label htmlFor="documents" className="file-upload-label">
                    <div className="upload-content">
                      <div className="upload-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                        </svg>
                      </div>
                      <div className="upload-text">
                        <p className="upload-primary">
                          <span className="upload-highlight">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </p>
                        <p className="upload-secondary">
                          PDF files only (Max 10MB each, up to 3 documents)
                        </p>
                      </div>
                    </div>
                    <input
                      onChange={handleDocuments}
                      accept=".pdf"
                      type="file"
                      id="documents"
                      ref={docInputRef}
                      aria-label="opportunity-documents"
                      className="hidden"
                      multiple
                      max={3}
                    />
                  </label>
                </div>

                {/* Display Uploaded Documents */}
                {files.documentFiles && files.documentFiles.length > 0 && (
                  <div className="uploaded-files">
                    <h4 className="uploaded-files-title">
                      Uploaded Documents ({files.documentFiles.length})
                    </h4>
                    <div className="uploaded-files-list">
                      {files.documentFiles.map((file, index) => (
                        <div
                          key={index}
                          className="uploaded-file-item document-item"
                        >
                          <div className="file-preview document-preview">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                            </svg>
                          </div>
                          <div className="file-info">
                            <p className="file-name">{file.name}</p>
                            <p className="file-size">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newFiles = files.documentFiles.filter(
                                (_, i) => i !== index
                              );
                              setFiles({ ...files, documentFiles: newFiles });
                            }}
                            className="remove-file-btn"
                          >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Opportunity Video */}
              <div className="enhanced-input-group">
                <label htmlFor="video" className="input-label">
                  Opportunity Video{" "}
                  <span className="optional-text">(Optional)</span>
                </label>
                <div className="file-upload-area">
                  <label htmlFor="video" className="file-upload-label">
                    <div className="upload-content">
                      <div className="upload-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z" />
                        </svg>
                      </div>
                      <div className="upload-text">
                        <p className="upload-primary">
                          <span className="upload-highlight">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </p>
                        <p className="upload-secondary">
                          MP4 or WebM files only (Max 50MB each, up to 3 videos)
                        </p>
                      </div>
                    </div>
                    <input
                      onChange={handleVideo}
                      type="file"
                      accept=".mp4, .webm"
                      id="video"
                      aria-label="opportunity-video"
                      className="hidden"
                      multiple
                      max={3}
                    />
                  </label>
                </div>

                {/* Display Uploaded Videos */}
                {files.videos && files.videos.length > 0 && (
                  <div className="uploaded-files">
                    <h4 className="uploaded-files-title">
                      Uploaded Videos ({files.videos.length})
                    </h4>
                    <div className="uploaded-files-list">
                      {files.videos.map((file, index) => (
                        <div
                          key={index}
                          className="uploaded-file-item video-item"
                        >
                          <div className="file-preview video-preview">
                            <video
                              src={URL.createObjectURL(file)}
                              className="video-preview-player"
                              controls
                              preload="metadata"
                            />
                          </div>
                          <div className="file-info">
                            <p className="file-name">{file.name}</p>
                            <p className="file-size">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newFiles = files.videos.filter(
                                (_, i) => i !== index
                              );
                              setFiles({ ...files, videos: newFiles });
                            }}
                            className="remove-file-btn"
                          >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="form-actions">
          <button type="submit" className="continue-button">
            <span>Submit Opportunity</span>
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default OpportunityDetails;
