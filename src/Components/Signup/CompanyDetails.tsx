import React from "react";
import { Im<PERSON>ross } from "react-icons/im";
import { countryList } from "../../shared/constants";

const CompanyDetails = ({
  companyFormik,
  uploadLogo,
  toggle_error,
  comp_logo,
  setUploadLogoM,
  setCompLogo,
}: {
  companyFormik: any;
  uploadLogo: boolean;
  toggle_error: () => void;
  comp_logo: File | null;
  setUploadLogoM: React.Dispatch<React.SetStateAction<boolean>>;
  setCompLogo: React.Dispatch<React.SetStateAction<File | null>>;
}) => {
  const handleLogoUpload = (event: any) => {
    setUploadLogoM(!uploadLogo);
    setCompLogo(event.target.files[0]);
  };

  const handleRemoveCompLogo = () => {
    setCompLogo(null);
    companyFormik.setFieldValue("logo", null);
  };

  const handleCompLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCompLogo(e.target.files?.[0] || null);
    companyFormik.setFieldValue("logo", e.target.files![0]);
  };

  return (
    <div className="enhanced-company-info">
      <div className="company-info-header">
        <div className="header-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z" />
          </svg>
        </div>
        <h1 className="info-title">Company Information</h1>
        <p className="info-subtitle">
          Tell us about your organization to complete your profile
        </p>
      </div>

      <form onSubmit={companyFormik.handleSubmit} className="company-info-form">
        <div className="form-grid">
          {/* Company Name Field */}
          <div className="enhanced-input-group">
            <label htmlFor="name" className="input-label">
              Company Name
            </label>
            <div className="input-wrapper">
              <div className="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z" />
                </svg>
              </div>
              <input
                type="text"
                id="name"
                name="name"
                value={companyFormik.values.name}
                onChange={companyFormik.handleChange}
                className="enhanced-input"
                placeholder="Acme Corporation"
                required
              />
            </div>
            {companyFormik.errors.name && companyFormik.touched.name && (
              <div className="input-helper">
                <span className="helper-text error">
                  {companyFormik.errors.name}
                </span>
              </div>
            )}
          </div>
          {/* Company Description Field */}
          <div className="enhanced-input-group">
            <label htmlFor="description" className="input-label">
              Company Description
            </label>
            <div className="textarea-wrapper">
              <div className="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
              </div>
              <textarea
                id="description"
                name="description"
                value={companyFormik.values.description}
                onChange={companyFormik.handleChange}
                className="enhanced-textarea"
                placeholder="Brief description of your company's mission and services..."
                rows={4}
                required
              />
            </div>
            {companyFormik.errors.description &&
              companyFormik.touched.description && (
                <div className="input-helper">
                  <span className="helper-text error">
                    {companyFormik.errors.description}
                  </span>
                </div>
              )}
          </div>
          {/* Website Field */}
          <div className="enhanced-input-group">
            <label htmlFor="website" className="input-label">
              Company Website
            </label>
            <div className="input-wrapper">
              <div className="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16.36,14C16.44,13.34 16.5,12.68 16.5,12C16.5,11.32 16.44,10.66 16.36,10H19.74C19.9,10.64 20,11.31 20,12C20,12.69 19.9,13.36 19.74,14M14.59,19.56C15.19,18.45 15.65,17.25 15.97,16H18.92C17.96,17.65 16.43,18.93 14.59,19.56M14.34,14H9.66C9.56,13.34 9.5,12.68 9.5,12C9.5,11.32 9.56,10.65 9.66,10H14.34C14.43,10.65 14.5,11.32 14.5,12C14.5,12.68 14.43,13.34 14.34,14M12,19.96C11.17,18.76 10.5,17.43 10.09,16H13.91C13.5,17.43 12.83,18.76 12,19.96M8,8H5.08C6.03,6.34 7.57,5.06 9.4,4.44C8.8,5.55 8.35,6.75 8,8M5.08,16H8C8.35,17.25 8.8,18.45 9.4,19.56C7.57,18.93 6.03,17.65 5.08,16M4.26,14C4.1,13.36 4,12.69 4,12C4,11.31 4.1,10.64 4.26,10H7.64C7.56,10.66 7.5,11.32 7.5,12C7.5,12.68 7.56,13.34 7.64,14M12,4.03C12.83,5.23 13.5,6.57 13.91,8H10.09C10.5,6.57 11.17,5.23 12,4.03M18.92,8H15.97C15.65,6.75 15.19,5.55 14.59,4.44C16.43,5.07 17.96,6.34 18.92,8M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                </svg>
              </div>
              <input
                type="url"
                id="website"
                name="website"
                value={companyFormik.values.website}
                onChange={companyFormik.handleChange}
                className="enhanced-input"
                placeholder="https://www.company.com"
                required
              />
            </div>
            {companyFormik.errors.website && companyFormik.touched.website && (
              <div className="input-helper">
                <span className="helper-text error">
                  {companyFormik.errors.website}
                </span>
              </div>
            )}
          </div>
          {/* Country Field */}
          <div className="enhanced-input-group">
            <label htmlFor="country" className="input-label">
              Country
            </label>
            <div className="input-wrapper">
              <div className="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
                </svg>
              </div>
              <select
                id="country"
                name="country"
                value={companyFormik.values.country}
                onChange={companyFormik.handleChange}
                className="enhanced-select"
                required
              >
                <option value="" disabled>
                  Select your country
                </option>
                {countryList.map((country, index) => (
                  <option key={index} value={country.symbol}>
                    {country.name}
                  </option>
                ))}
              </select>
            </div>
            {companyFormik.errors.country && companyFormik.touched.country && (
              <div className="input-helper">
                <span className="helper-text error">
                  {companyFormik.errors.country}
                </span>
              </div>
            )}
          </div>
          {/* Company Logo Field */}
          <div className="enhanced-input-group">
            <label htmlFor="logo" className="input-label">
              Company Logo
            </label>
            {comp_logo ? (
              <div className="uploaded-file-display">
                <div className="file-info">
                  <div className="file-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    </svg>
                  </div>
                  <div className="file-details">
                    <span className="file-name">
                      {comp_logo.name.length > 25
                        ? `${comp_logo.name.substring(0, 25)}...`
                        : comp_logo.name}
                    </span>
                    <span className="file-size">
                      {(comp_logo.size / 1024).toFixed(1)} KB
                    </span>
                  </div>
                </div>
                <button
                  onClick={handleRemoveCompLogo}
                  type="button"
                  className="remove-file-btn"
                  aria-label="Remove logo"
                >
                  <ImCross />
                </button>
              </div>
            ) : (
              <div className="file-upload-area">
                <label htmlFor="logo" className="file-upload-label">
                  <div className="upload-content">
                    <div className="upload-icon">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                      </svg>
                    </div>
                    <div className="upload-text">
                      <p className="upload-primary">
                        <span className="upload-highlight">
                          Click to upload
                        </span>{" "}
                        or drag and drop
                      </p>
                      <p className="upload-secondary">
                        PNG files only (Max 5MB)
                      </p>
                    </div>
                  </div>
                  <input
                    id="logo"
                    type="file"
                    className="hidden"
                    accept="image/png"
                    onChange={handleCompLogoChange}
                  />
                </label>
              </div>
            )}
            {companyFormik.errors.logo && companyFormik.touched.logo && (
              <div className="input-helper">
                <span className="helper-text error">
                  {companyFormik.errors.logo}
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="form-actions">
          <button type="submit" className="continue-button">
            <span>Continue</span>
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanyDetails;

// {uploadLogo ? (
//   <div className="z-10 flex absolute duration-200 ease-in-out w-full h-full justify-center bg-slate-700 bg-opacity-30">
//     <input
//       id="upload_logo_file"
//       onChange={(e) => {
//         handleLogoUpload(e);
//       }}
//       type="file"
//       accept="image/*"
//       style={{ display: "none" }}
//     />
//     <label
//       htmlFor="upload_logo_file"
//       className="flex duration-150 -translate-y-20 justify-center items-center w-2/3 "
//     >
//       <img
//         className="w-2/3 h-1/2"
//         src={upload_logo_baner}
//         alt="Upload Logo"
//       />
//     </label>
//   </div>
// ) : null}
// <div id="error-modal" tabIndex={-1} className="hidden" aria-hidden="true">
//   <div className="main -translate-y-1/2 relative  w-full max-w-md h-full md:h-auto">
//     <div className="relative bg-white rounded-lg">
//       <button
//         type="button"
//         onClick={() => {
//           toggle_error();
//         }}
//         className="absolute top-3 right-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-800 dark:hover:text-white"
//         data-modal-toggle="error-modal"
//       >
//         <svg
//           aria-hidden="true"
//           className="w-5 h-5"
//           fill="currentColor"
//           viewBox="0 0 20 20"
//           xmlns="http://www.w3.org/2000/svg"
//         >
//           <path
//             fillRule="evenodd"
//             d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
//             clipRule="evenodd"
//           ></path>
//         </svg>
//         <span className="sr-only">Close modal</span>
//       </button>
//       <div className="p-6 text-center">
//         <svg
//           aria-hidden="true"
//           className="mx-auto mb-4 w-14 h-14 text-gray-400 dark:text-gray-200"
//           fill="none"
//           stroke="currentColor"
//           viewBox="0 0 24 24"
//           xmlns="http://www.w3.org/2000/svg"
//         >
//           <path
//             strokeLinecap="round"
//             strokeLinejoin="round"
//             strokeWidth="2"
//             d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
//           ></path>
//         </svg>
//         <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
//           Some error occurred while submiting your details...
//         </h3>
//         <button
//           data-modal-toggle="error-modal"
//           onClick={() => {
//             toggle_error();
//           }}
//           type="button"
//           className="text-white bg-blue-600 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center mr-2"
//         >
//           OK
//         </button>
//       </div>
//     </div>
//   </div>
// </div>
