import { Editor } from "@tinymce/tinymce-react";
import { useFormik } from "formik";
import React, { Dispatch, useEffect, useRef, useState } from "react";
import { getSubSectorBySector } from "../../store/actioncreators/sub-sectoractions";
import { useDispatch } from "react-redux";
import {
  CONTENT_TYPE,
  FILE_PATH,
  FILE_TYPE,
  MyFormValues,
  developmentStage,
  files,
  iprStatus,
  presignedData,
  productItem,
  sectorItem,
  subsectorItem,
} from "../constants";
import { useSelector } from "react-redux";
import { productSchema } from "../validations/productValidations";
import { RequestMethods } from "../../shared/RequestMethods";
import axios from "axios";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { notify } from "../../utils";
import {
  createProduct,
  createUserProduct,
} from "../../store/actioncreators/productactions";
import createProductLogo from "../../assests/images/signup/create_product.svg";
import {
  getSector,
  getSectors,
} from "../../store/actioncreators/sectoractions";
import { Spinner } from "../utils/loader";
import CustomEditor from "../shared/CustomEditor";

const TechnologyDetails = ({
  handleTechnologySubmit,
  handleSuccessModal,
}: {
  handleTechnologySubmit: () => Promise<string>;
  handleSuccessModal: (isOpen: boolean, state: string, message: string) => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const [sectors, setSectors] = useState<sectorItem[]>([]);
  const docInputRef = useRef<HTMLInputElement>(null);
  const vidInputRef = useRef<HTMLInputElement>(null);

  const handleFetchSectors = async () => {
    const fetchedSectors = await getSectors();
    setSectors(fetchedSectors);
  };

  useEffect(() => {
    handleFetchSectors();
  }, []);

  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );
  const [iprStatusCheckbox, setIprStatusCheckbox] = useState(iprStatus);

  const initialValues: MyFormValues = {
    name: "",
    description: "",
    sectorId: sectors.length ? sectors[0]._id : "",
    subSectorId: "",
    developmentStage: developmentStage[0].value,
    iprStatus: [],
  };

  const [files, setFiles] = useState<files>({
    image: false,
    document: false,
    imageFile: [],
    documents: [],
    videos: [],
  });

  const fetchSubsector = async (sectorId: string, currentId: string) => {
    if (currentId !== sectorId || currentId === "")
      dispatch(getSubSectorBySector(sectorId));
  };

  useEffect(() => {
    if (sectors?.length) {
      fetchSubsector(sectors[0]?._id, "");
    }
  }, [sectors]);

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: RequestMethods.POST,
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = error.message;
        dispatch(failToast());
      });

    return result;
  };

  const postLogo = async (signed: string, imageFile: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: imageFile,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const uploadVideo = async (selectedFile: any, presignedUrl: string) => {
    if (!selectedFile) {
      return;
    }

    try {
      await axios.put(presignedUrl, selectedFile, {
        headers: {
          "Content-Type": selectedFile.type,
          "Access-Control-Allow-Origin": true,
        },
      });
    } catch (error) {
      notify("Failed to upload video", "error");
    }
  };

  const postDocument = async (signed: string, file: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": file.type,
        "Access-Control-Allow-Origin": true,
      },
      data: file,
    };

    await axios(config)
      .then(function (response) {})
      .catch(function (error) {});
  };

  const getIprStatus = () => {
    const iprStatus = [];
    for (let i = 0; i < iprStatusCheckbox.length; i++) {
      if (iprStatusCheckbox[i].checked) {
        iprStatus.push(iprStatusCheckbox[i].value);
      }
    }
    return iprStatus;
  };

  function handleIprStatus(id: Number) {
    const updatedOptions = iprStatusCheckbox.map((option) => {
      if (option.id === id) {
        return { ...option, checked: !option.checked };
      }
      return option;
    });
    setIprStatusCheckbox(updatedOptions);
  }

  const handleDocuments = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files || [];
    if (!fileList) return;

    const documents = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      documents.push(fileList[i]);
    }

    setFiles({ ...files, documents, document: false });
  };

  const handleImage = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files || [];
    if (!fileList) return;

    const images = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      images.push(fileList[i]);
    }

    setFiles({ ...files, imageFile: images, image: false });
  };

  const handleVideo = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e?.target.files || [];
    if (!fileList) return;

    const videos = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      const fileSizeInMB = fileList[i].size / (1024 * 1024); // Convert file size to MB
      if (fileSizeInMB <= 50) {
        videos.push(fileList[i]);
      } else {
        alert("Please select a file smaller than 50 MB.");
      }
    }

    setFiles({ ...files, videos });
  };

  const handleCreate = async (values: MyFormValues) => {
    try {
      handleSuccessModal(true, "LOADING", "Please Wait");

      const userId = await handleTechnologySubmit();
      if (!userId) return;

      if (!files.imageFile) {
        return setFiles({ ...files, image: true });
      }
      setFiles({ ...files, document: false, image: false });
      let productImages: string[] = [];
      let videos: string[] = [];
      let documents: string[] = [];
      for (const file of files.imageFile) {
        const signedLogoData: presignedData = {
          fileName: file.name ?? values.name,
          filePath: FILE_PATH.PRODUCTS_IMAGE,
          fileType: FILE_TYPE.PNG,
        };
        let imageUrl = await getPresigned(signedLogoData);
        await postLogo(imageUrl, file);
        productImages.push(imageUrl.split("?")[0]);
      }
      for (const file of files.videos) {
        const videoData: presignedData = {
          fileName: file.name ?? values.name,
          filePath: FILE_PATH.PRODUCTS_VIDEO,
          fileType: file.type ?? "",
        };
        let videoUrl = await getPresigned(videoData);
        await uploadVideo(file, videoUrl);
        videos.push(videoUrl.split("?")[0]);
      }
      for (const file of files.documents) {
        const signedDocumentData: presignedData = {
          fileName: file.name || values.name,
          filePath: FILE_PATH.COMPANY_DOCS,
          fileType: FILE_TYPE.PDF,
        };
        let documentUrl = await getPresigned(signedDocumentData);
        await postDocument(documentUrl, file);
        documents.push(documentUrl.split("?")[0]);
      }
      const data: productItem = {
        name: values.name,
        description: values.description,
        images: productImages,
        sectorId: values.sectorId,
        subSectorId: values.subSectorId,
        developmentStage: values.developmentStage,
        iprStatus: getIprStatus(),
        videos,
        documents,
      };
      const result = await (dispatch as any)(createUserProduct(data, userId));

      if (result?.success) {
        handleSuccessModal(
          true,
          "SUCCESS",
          "An email has been sent on your mail address, please verify it within 24 hours."
        );
      } else {
        handleSuccessModal(
          true,
          "ERROR",
          result?.message || "Failed to Register"
        );
      }
    } catch (err) {
      handleSuccessModal(true, "ERROR", "Failed to Register");
    }
  };

  const technologyformik = useFormik<MyFormValues>({
    initialValues: initialValues,
    validationSchema: productSchema,
    onSubmit: (values, formikHelpers) => {
      handleCreate(values);
    },
  });

  return (
    <div className="enhanced-technology-details">
      <div className="technology-details-header">
        <div className="header-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z" />
          </svg>
        </div>
        <h1 className="info-title">Technology Details</h1>
        <p className="info-subtitle">
          Showcase your innovative technology to connect with potential
          investors and partners
        </p>
      </div>

      <form
        onSubmit={technologyformik.handleSubmit}
        className="technology-details-form"
      >
        <div className="form-grid">
          {/* Basic Information Section */}
          <div className="form-section">
            <div className="section-header">
              <h2 className="section-title">Basic Information</h2>
              <p className="section-subtitle">
                Provide the essential details about your technology
              </p>
            </div>

            {/* Technology Name Field */}
            <div className="enhanced-input-group">
              <label htmlFor="name" className="input-label">
                Technology Name *
              </label>
              <div className="input-wrapper">
                <div className="input-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9,10V12H7V10A5,5 0 0,1 12,5A5,5 0 0,1 17,10V12H15V10A3,3 0 0,0 12,7A3,3 0 0,0 9,10M6,12V22H18V12H6M16,20H8V14H16V20Z" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={technologyformik.values.name}
                  onChange={(e) =>
                    technologyformik.setFieldValue("name", e.target.value)
                  }
                  className="enhanced-input"
                  placeholder="Enter your technology name"
                  required
                />
              </div>
              {technologyformik.errors.name &&
                technologyformik.touched.name && (
                  <div className="input-helper">
                    <span className="helper-text error">
                      {technologyformik.errors.name}
                    </span>
                  </div>
                )}
            </div>
            {/* Technology Description Field */}
            <div className="enhanced-input-group">
              <label htmlFor="description" className="input-label">
                Detailed Description *
              </label>
              <div className="editor-wrapper">
                <CustomEditor
                  initialContent={technologyformik.values.description}
                  onChange={(content: string) => {
                    technologyformik.setFieldValue("description", content);
                  }}
                />
              </div>
              {technologyformik.errors.description &&
                technologyformik.touched.description && (
                  <div className="input-helper">
                    <span className="helper-text error">
                      {technologyformik.errors.description}
                    </span>
                  </div>
                )}
              <div className="input-helper">
                <span className="helper-text">
                  Provide comprehensive details about your technology, including
                  features, benefits, and applications.
                </span>
              </div>
            </div>
          </div>
          {/* Classification Section */}
          <div className="form-section">
            <div className="section-header">
              <h2 className="section-title">Classification</h2>
              <p className="section-subtitle">
                Categorize your technology for better discoverability
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Sector Field */}
              <div className="enhanced-input-group">
                <label htmlFor="sectorId" className="input-label">
                  Sector *
                </label>
                <div className="input-wrapper">
                  <div className="input-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
                    </svg>
                  </div>
                  <select
                    id="sectorId"
                    name="sectorId"
                    value={technologyformik.values.sectorId}
                    onChange={(e) => {
                      fetchSubsector(
                        e.target.value,
                        technologyformik.values.sectorId
                      );
                      technologyformik.setFieldValue(
                        "sectorId",
                        e.target.value
                      );
                    }}
                    className="enhanced-select"
                    required
                  >
                    <option value="">Select a sector</option>
                    {sectors.map((item: sectorItem, id) => {
                      return (
                        <option key={id} value={item._id}>
                          {item.name}
                        </option>
                      );
                    })}
                  </select>
                </div>
                {technologyformik.errors.sectorId &&
                  technologyformik.touched.sectorId && (
                    <div className="input-helper">
                      <span className="helper-text error">
                        {technologyformik.errors.sectorId}
                      </span>
                    </div>
                  )}
              </div>
              {/* Sub Sector Field */}
              {subsectorlist.SUB_SECTOR_LIST?.length ? (
                <div className="enhanced-input-group">
                  <label htmlFor="subSectorId" className="input-label">
                    Sub Sector *
                  </label>
                  <div className="input-wrapper">
                    <div className="input-icon">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
                      </svg>
                    </div>
                    <select
                      id="subSectorId"
                      name="subSectorId"
                      value={technologyformik.values.subSectorId}
                      onChange={(e) =>
                        technologyformik.setFieldValue(
                          "subSectorId",
                          e.target.value
                        )
                      }
                      className="enhanced-select"
                      required
                    >
                      <option value="">Select a sub-sector</option>
                      {subsectorlist.SUB_SECTOR_LIST.map(
                        (item: subsectorItem, id) => {
                          return (
                            <option key={id} value={item._id}>
                              {item.name}
                            </option>
                          );
                        }
                      )}
                    </select>
                  </div>
                </div>
              ) : null}
            </div>
          </div>
          {/* Development & Status Section */}
          <div className="form-section">
            <div className="section-header">
              <h2 className="section-title">Development & Status</h2>
              <p className="section-subtitle">
                Provide information about your technology's current stage and
                intellectual property
              </p>
            </div>

            <div className="space-y-6">
              {/* Development Stage Field */}
              <div className="enhanced-input-group">
                <label htmlFor="developmentStage" className="input-label">
                  Development Stage *
                </label>
                <div className="input-wrapper">
                  <div className="input-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z" />
                    </svg>
                  </div>
                  <select
                    id="developmentStage"
                    name="developmentStage"
                    value={technologyformik.values.developmentStage}
                    onChange={(e) =>
                      technologyformik.setFieldValue(
                        "developmentStage",
                        e.target.value
                      )
                    }
                    className="enhanced-select"
                    required
                  >
                    <option value="">Select development stage</option>
                    {developmentStage.map((stage, id) => {
                      return (
                        <option key={id} value={stage.value}>
                          {stage.value}
                        </option>
                      );
                    })}
                  </select>
                </div>
              </div>

              {/* IPR Status Field */}
              <div className="enhanced-input-group">
                <label className="input-label">IPR Status *</label>
                <div className="checkbox-group">
                  {iprStatusCheckbox.map((option) => (
                    <label className="checkbox-item" key={option.id}>
                      <input
                        type="checkbox"
                        className="checkbox-input"
                        checked={option.checked}
                        onChange={() => handleIprStatus(option.id)}
                      />
                      <span className="checkbox-label">{option.value}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Media & Documents Section */}
          <div className="form-section">
            <div className="section-header">
              <h2 className="section-title">Media & Documents</h2>
              <p className="section-subtitle">
                Upload images, documents, and videos to showcase your technology
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Product Images */}
              <div className="enhanced-input-group">
                <label htmlFor="logo" className="input-label">
                  Product Images *
                </label>
                <div className="file-upload-area">
                  <label htmlFor="logo" className="file-upload-label">
                    <div className="upload-content">
                      <div className="upload-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M9,12C9,13.38 10.22,14.5 11.75,14.5C13.28,14.5 14.5,13.38 14.5,12C14.5,10.62 13.28,9.5 11.75,9.5C10.22,9.5 9,10.62 9,12M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16,16.5L11.5,12L9,14.5L8,13.5L11.5,10L16,14.5V16.5Z" />
                        </svg>
                      </div>
                      <div className="upload-text">
                        <p className="upload-primary">
                          <span className="upload-highlight">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </p>
                        <p className="upload-secondary">
                          PNG files only (Max 5MB each, up to 3 images)
                        </p>
                      </div>
                    </div>
                    <input
                      onChange={handleImage}
                      accept=".png"
                      type="file"
                      id="logo"
                      aria-label="product-images"
                      className="hidden"
                      multiple
                      max={3}
                    />
                  </label>
                </div>
                {files.image && (
                  <div className="input-helper">
                    <span className="helper-text error">
                      Please upload product images
                    </span>
                  </div>
                )}

                {/* Display Uploaded Images */}
                {files.imageFile && files.imageFile.length > 0 && (
                  <div className="uploaded-files">
                    <h4 className="uploaded-files-title">
                      Uploaded Images ({files.imageFile.length})
                    </h4>
                    <div className="uploaded-files-grid">
                      {files.imageFile.map((file, index) => (
                        <div key={index} className="uploaded-file-item">
                          <div className="file-preview">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Product ${index + 1}`}
                              className="image-preview"
                            />
                          </div>
                          <div className="file-info">
                            <p className="file-name">{file.name}</p>
                            <p className="file-size">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newFiles = files.imageFile.filter(
                                (_, i) => i !== index
                              );
                              setFiles({ ...files, imageFile: newFiles });
                            }}
                            className="remove-file-btn"
                          >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Product Documents */}
              <div className="enhanced-input-group">
                <label htmlFor="documents" className="input-label">
                  Product Documents{" "}
                  <span className="optional-text">(Optional)</span>
                </label>
                <div className="file-upload-area">
                  <label htmlFor="documents" className="file-upload-label">
                    <div className="upload-content">
                      <div className="upload-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                        </svg>
                      </div>
                      <div className="upload-text">
                        <p className="upload-primary">
                          <span className="upload-highlight">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </p>
                        <p className="upload-secondary">
                          PDF files only (Max 10MB each, up to 3 documents)
                        </p>
                      </div>
                    </div>
                    <input
                      onChange={handleDocuments}
                      accept=".pdf"
                      type="file"
                      id="documents"
                      ref={docInputRef}
                      aria-label="product-documents"
                      className="hidden"
                      multiple
                      max={3}
                    />
                  </label>
                </div>

                {/* Display Uploaded Documents */}
                {files.documents && files.documents.length > 0 && (
                  <div className="uploaded-files">
                    <h4 className="uploaded-files-title">
                      Uploaded Documents ({files.documents.length})
                    </h4>
                    <div className="uploaded-files-list">
                      {files.documents.map((file, index) => (
                        <div
                          key={index}
                          className="uploaded-file-item document-item"
                        >
                          <div className="file-preview document-preview">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                            </svg>
                          </div>
                          <div className="file-info">
                            <p className="file-name">{file.name}</p>
                            <p className="file-size">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newFiles = files.documents.filter(
                                (_, i) => i !== index
                              );
                              setFiles({ ...files, documents: newFiles });
                            }}
                            className="remove-file-btn"
                          >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Product Video */}
              <div className="enhanced-input-group">
                <label htmlFor="video" className="input-label">
                  Product Video{" "}
                  <span className="optional-text">(Optional)</span>
                </label>
                <div className="file-upload-area">
                  <label htmlFor="video" className="file-upload-label">
                    <div className="upload-content">
                      <div className="upload-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                          <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z" />
                        </svg>
                      </div>
                      <div className="upload-text">
                        <p className="upload-primary">
                          <span className="upload-highlight">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </p>
                        <p className="upload-secondary">
                          MP4 or WebM files only (Max 50MB each, up to 3 videos)
                        </p>
                      </div>
                    </div>
                    <input
                      onChange={handleVideo}
                      type="file"
                      accept=".mp4, .webm"
                      id="video"
                      ref={vidInputRef}
                      aria-label="product-video"
                      className="hidden"
                      multiple
                      max={3}
                    />
                  </label>
                </div>

                {/* Display Uploaded Videos */}
                {files.videos && files.videos.length > 0 && (
                  <div className="uploaded-files">
                    <h4 className="uploaded-files-title">
                      Uploaded Videos ({files.videos.length})
                    </h4>
                    <div className="uploaded-files-list">
                      {files.videos.map((file, index) => (
                        <div
                          key={index}
                          className="uploaded-file-item video-item"
                        >
                          <div className="file-preview video-preview">
                            <video
                              src={URL.createObjectURL(file)}
                              className="video-preview-player"
                              controls
                              preload="metadata"
                            />
                          </div>
                          <div className="file-info">
                            <p className="file-name">{file.name}</p>
                            <p className="file-size">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newFiles = files.videos.filter(
                                (_, i) => i !== index
                              );
                              setFiles({ ...files, videos: newFiles });
                            }}
                            className="remove-file-btn"
                          >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                              <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                            </svg>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="form-actions">
          <button type="submit" className="continue-button">
            <span>Submit Technology</span>
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default TechnologyDetails;
