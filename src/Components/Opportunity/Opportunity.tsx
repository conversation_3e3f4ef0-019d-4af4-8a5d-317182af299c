import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation, useParams, useNavigate } from "react-router-dom";
import ReactCountryFlag from "react-country-flag";
import Slider from "react-slick";
import { useSelector } from "react-redux";
import { BiLinkExternal } from "react-icons/bi";
import axios from "axios";
import { Helmet } from "react-helmet";

import { NONE, oppotunityItemFullFetched } from "../constants";
import productbanner from "../../assests/banners/product_banner_alt.png";
import {
  followUser,
  unfollowUser,
} from "../../store/actioncreators/followactions";
import { sendConnection } from "../../store/actioncreators/connectionactions";
import PdfDownloader from "../../shared/PdfDownloader";
import RenderHTML from "../utils/RenderHTML";
import companyLogo from "../../assests/banners/company_logo.png";
import { PROFILE_TYPES } from "../../shared/enum";

const Opportunity = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  const sidebar_carousal_settings = {
    dots: true,
    infinite: true,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    swipeToSlide: true,
    autoplay: false,
  };

  const dispatch: Dispatch<any> = useDispatch();

  let [opp, setOpp] = useState<oppotunityItemFullFetched>({
    _id: "",
    technologyPartnerRequirement: "",
    description: "",
    image: "",
    displayOnHomePage: false,
    isApprovedBySubAdmin: false,
    isApprovedByAdmin: false,
    isRejected: false,
    document: "",
    sectorId: "",
    subSectorId: "",
    userId: "",
    createdAt: "",
    images: [],
    documents: [],
    __v: -1,
    users: {
      _id: "",
      fullName: "",
      email: "",
      phoneNumber: "",
      countryCode: "",
      referenceCode: "",
      isEmailVerified: false,
      isUserVerified: false,
      isRejected: false,
      password: "",
      userRole: -1,
      userType: "",
      companyId: "",
      follower: [],
      following: [],
      connections: [
        {
          connectionStatus: "",
          userId: "",
        },
      ],
      createdAt: "",
      __v: -1,
    },
    company: {
      _id: "",
      name: "",
      logo: "",
      description: "",
      address: "",
      website: "",
      country: "",
      companyTurnover: "",
      companyId: "",
      typeAndSizeOfPartnersRequired: [],
      typesOfPartnershipConsidered: [],
      createdAt: "",
      iprStatus: [],
      __v: -1,
    },
    sectors: {
      _id: "",
      name: "",
      slug: "",
      image: "",
      createdAt: "",
      __v: -1,
    },
    subsectors: {
      _id: "",
      name: "",
      slug: "",
      sectorId: "",
      createdAt: "",
      __v: -1,
    },
  });
  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const location = useLocation();
  const navigate = useNavigate();

  const [following, setFollowing] = useState(false);
  const [connected, setConnection] = useState("Connect");

  const addQuery = async (companyName: string, userType: string) => {
    const regex = /[!@#$%^&*()_+{}[\]:;<>,.?~\\/-]/g;

    const queryParams = new URLSearchParams(location.search);
    queryParams.set(
      "company",
      companyName.toLocaleLowerCase().replaceAll(regex, "").replaceAll(" ", "_")
    );
    queryParams.set("type", userType.toLocaleLowerCase());
    const newSearchString = queryParams.toString();
    navigate({
      pathname: location.pathname,
      search: newSearchString,
    });
  };

  const loadProduct = (id: string) => {
    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/opportunities/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setOpp(response.data);
        isFollowing(response.data?.users?._id);
        isConnected(response.data?.users?._id);
      })
      .catch(function (error) {});
  };

  const isFollowing = (userid: string) => {
    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/checkisfollowing/${userid}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setFollowing(response.data);
      })
      .catch(function (error) {});
  };

  const isConnected = (userid: string) => {
    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/checkisconnected/${userid}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setConnection(response.data);
      })
      .catch(function (error) {});
  };

  const { id } = useParams();
  let opportunityId: string = id ? id : "";

  useEffect(() => {
    loadProduct(opportunityId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (opp?.company?.name) addQuery(opp?.company?.name, opp?.users?.userType);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [opp]);

  const DOC = new Date(opp.createdAt);

  const handleFollow = () => {
    if (user.admin !== -1) {
      if (!following) {
        dispatch(followUser(opp.userId));
        setFollowing(true);
      }
      return;
    }
    handleLoginModal();
  };

  const handleUnfollow = () => {
    if (user.admin !== -1) {
      if (following) {
        dispatch(unfollowUser(opp.userId));
        setFollowing(false);
      }
      return;
    }
    handleLoginModal();
  };

  const handleConnect = () => {
    if (user.admin !== -1) {
      if (connected === "Connect") {
        dispatch(sendConnection(opp.userId));
        setConnection("Connection Requested");
      }
      return;
    }
    handleLoginModal();
  };

  return (
    <React.Fragment>
      <Helmet>
        <title>{opp?.company?.name}</title>
        <meta
          name="description"
          key="description"
          content={
            opp?.description ? opp?.description : opp?.company?.description
          }
        />
        <meta name="title" key="title" content={opp?.company?.name} />
        <meta property="og:title" content={opp.company.name} />
        <meta
          property="og:description"
          content={opp.description ? opp.description : opp.company.description}
        />
        <meta
          property="og:image"
          content={opp.image === NONE ? productbanner : opp.image}
        />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/opportunity/${opp._id}`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={opp?.company?.name} />
        <meta
          name="twitter:description"
          content={
            opp?.description ? opp?.description : opp?.company?.description
          }
        />
        <meta
          name="twitter:image"
          content={opp?.image === NONE ? productbanner : opp?.image}
        />
        <meta name="twitter:card" content={opp?.company?.name} />
      </Helmet>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <>
          {/* Breadcrumb Navigation */}
          <div className="bg-white border-b border-gray-100 mt-8">
            <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <nav className="flex" aria-label="Breadcrumb">
                <ol className="inline-flex items-center space-x-1 md:space-x-3">
                  <li className="inline-flex items-center">
                    <a
                      href="/"
                      className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors"
                    >
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                      </svg>
                      Home
                    </a>
                  </li>
                  <li>
                    <div className="flex items-center">
                      <svg
                        className="w-6 h-6 text-gray-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      <a
                        href="/opportunities"
                        className="ml-1 text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default md:ml-2 transition-colors"
                      >
                        Opportunities
                      </a>
                    </div>
                  </li>
                  <li>
                    <div className="flex items-center">
                      <svg
                        className="w-6 h-6 text-gray-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 truncate max-w-xs">
                        {opp.technologyPartnerRequirement?.length > 50
                          ? `${opp.technologyPartnerRequirement.substring(
                              0,
                              50
                            )}...`
                          : opp.technologyPartnerRequirement}
                      </span>
                    </div>
                  </li>
                </ol>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Main Content */}
              <div className="lg:col-span-2 space-y-8">
                {/* Header Section */}
                <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex-1">
                      <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 font-roboto mb-4">
                        Technology Partnership Opportunity
                      </h1>

                      {/* Company Info */}
                      {opp.company?.name && (
                        <div className="flex items-center space-x-4 mb-6">
                          <div className="flex items-center space-x-2">
                            <img
                              src={
                                !opp.company?.logo
                                  ? companyLogo
                                  : opp.company.logo
                              }
                              alt={opp.company?.name}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <div>
                              <p className="text-lg font-semibold text-gray-900">
                                {opp.company?.name}
                              </p>
                              <div className="flex items-center space-x-2">
                                {opp?.company?.country && (
                                  <ReactCountryFlag
                                    countryCode={opp.company?.country}
                                    svg
                                    style={{ width: "1em", height: "1em" }}
                                  />
                                )}
                                <span className="text-sm text-gray-600">
                                  {opp?.company?.country}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Posted Date */}
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-6">
                        <div className="flex items-center space-x-1">
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <span>
                            Posted on{" "}
                            {DOC.toLocaleString("default", {
                              month: "long",
                              day: "2-digit",
                              year: "numeric",
                            })}
                          </span>
                        </div>
                      </div>

                      {/* Sector and Sub-Sector Tags */}
                      <div className="flex flex-wrap gap-2 mb-6">
                        {opp.sectors?.name && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-GTI-BLUE-default/10 text-GTI-BLUE-default">
                            {opp.sectors.name}
                          </span>
                        )}
                        {opp.subsectors?.name && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            {opp.subsectors.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Technology Requirement Section */}
                <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    Technology Requirement
                  </h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed mb-6">
                      {opp?.technologyPartnerRequirement?.replace(
                        /(<([^>]+)>)/gi,
                        ""
                      )}
                    </p>
                    {opp.description && (
                      <div className="text-gray-700">
                        <RenderHTML html={opp.description} />
                      </div>
                    )}
                  </div>
                </div>

                {/* Company Description */}
                {opp.company?.description && (
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">
                      About {opp.company?.name}
                    </h2>
                    <p className="text-gray-700 leading-relaxed">
                      {opp.company?.description}
                    </p>
                  </div>
                )}
              </div>

              {/* Right Column - Sidebar */}
              <div className="lg:col-span-1 space-y-6">
                {/* Image Gallery */}
                {(opp?.images?.length > 0 || opp?.image) && (
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Media Gallery
                    </h3>
                    <div className="space-y-4">
                      {opp?.images?.length > 0 ? (
                        <Slider
                          {...sidebar_carousal_settings}
                          className="rounded-xl overflow-hidden"
                        >
                          {opp.images.map((image, index) => (
                            <div key={index} className="relative">
                              <img
                                className="w-full h-64 object-cover rounded-xl"
                                src={image}
                                alt={`${opp.company?.name} gallery ${
                                  index + 1
                                }`}
                              />
                            </div>
                          ))}
                        </Slider>
                      ) : (
                        <div className="relative">
                          <img
                            className="w-full h-64 object-cover rounded-xl"
                            src={opp.image === NONE ? productbanner : opp.image}
                            alt={opp.company?.name}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Documents Section */}
                {(opp?.document || opp?.documents?.length > 0) && (
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Documents
                    </h3>
                    <div className="space-y-3">
                      {opp?.document && (
                        <PdfDownloader
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                          url={opp?.document}
                        >
                          <div className="flex items-center space-x-3">
                            <svg
                              className="w-5 h-5 text-red-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                              />
                            </svg>
                            <span className="text-sm font-medium text-gray-900">
                              Document
                            </span>
                          </div>
                          <BiLinkExternal className="w-4 h-4 text-gray-500" />
                        </PdfDownloader>
                      )}
                      {opp?.documents?.map((document, index) => (
                        <PdfDownloader
                          key={index}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                          url={document}
                        >
                          <div className="flex items-center space-x-3">
                            <svg
                              className="w-5 h-5 text-red-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                              />
                            </svg>
                            <span className="text-sm font-medium text-gray-900">
                              Document {index + 1}
                            </span>
                          </div>
                          <BiLinkExternal className="w-4 h-4 text-gray-500" />
                        </PdfDownloader>
                      ))}
                    </div>
                  </div>
                )}
                {/* Contact/Action Section */}
                <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Get in Touch
                  </h3>

                  {/* Only show Connect/Follow if not viewing own opportunity */}
                  {opp?.userId !== user?.id && (
                    <div className="space-y-3 mb-4">
                      {/* Connect Button */}
                      {user.userType !== PROFILE_TYPES.GENERAL_SUBSCRIBER && (
                        <button
                          onClick={handleConnect}
                          disabled={connected === "Connection Requested"}
                          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center ${
                            connected === "Connected"
                              ? "bg-green-600 text-white hover:bg-green-700"
                              : connected === "Connection Requested"
                              ? "bg-gray-400 text-white cursor-not-allowed"
                              : "bg-GTI-BLUE-default text-white hover:bg-blue-700"
                          }`}
                        >
                          {connected === "Connected" ? (
                            <>
                              <svg
                                className="w-5 h-5 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                />
                              </svg>
                              Message
                            </>
                          ) : (
                            <>
                              <svg
                                className="w-5 h-5 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                                />
                              </svg>
                              {connected}
                            </>
                          )}
                        </button>
                      )}

                      {/* Follow Button */}
                      <button
                        onClick={following ? handleUnfollow : handleFollow}
                        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center ${
                          following
                            ? "bg-gray-600 text-white hover:bg-gray-700"
                            : "bg-blue-600 text-white hover:bg-blue-700"
                        }`}
                      >
                        {following ? (
                          <>
                            <svg
                              className="w-5 h-5 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            Following
                          </>
                        ) : (
                          <>
                            <svg
                              className="w-5 h-5 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                              />
                            </svg>
                            Follow
                          </>
                        )}
                      </button>
                    </div>
                  )}

                  {/* Share Opportunity Button */}
                  <button className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center">
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                    Share Opportunity
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      </div>
    </React.Fragment>
  );
};

export default Opportunity;
