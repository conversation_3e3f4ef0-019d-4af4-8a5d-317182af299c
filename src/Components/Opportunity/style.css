/* Modern Button Styles */
.button {
  @apply font-medium rounded-xl text-sm px-6 py-3 mx-1 mb-2 focus:outline-none transition-all duration-200 shadow-sm hover:shadow-md;
}

.active {
  @apply bg-GTI-BLUE-default text-white hover:bg-blue-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
}

.not-active {
  @apply bg-white text-GTI-BLUE-default border border-gray-200 hover:border-GTI-BLUE-default/30 hover:bg-gray-50;
}

/* Modern Opportunity Card Styles */
.opp-card-main {
  @apply flex flex-col duration-300 border border-gray-200 justify-start items-stretch shadow-sm hover:shadow-xl hover:shadow-GTI-BLUE-default/20 space-y-0 rounded-2xl cursor-pointer bg-white transform hover:-translate-y-1 transition-all overflow-hidden;
  width: 100%;
}

.opp-card-img {
  @apply flex w-full overflow-hidden rounded-t-2xl;
  height: 200px;
}

.opp-card-img img {
  @apply w-full h-full object-cover transition-transform duration-300 hover:scale-105;
}

.opp-card-content {
  @apply flex flex-col p-6 space-y-4 flex-1;
}

.opp-card-title {
  @apply flex flex-col space-y-2;
}

.opp-card-button {
  @apply w-full flex flex-row justify-center items-center;
}

/* Modern Grid Layout */
.opp-list-main {
  @apply grid w-full container-spacing mx-auto gap-6 lg:grid-cols-3 md:grid-cols-2 grid-cols-1;
}

.product-single-main {
  @apply flex flex-col lg:px-20 px-10 w-full py-5 h-full space-y-5 items-center;
}
.product-single-banner {
  @apply flex w-full shadow shadow-slate-500 justify-items-start  lg:h-96  md:h-48 sm:h-24 rounded object-cover relative;
}
.product-single-parent-details {
  @apply flex flex-col w-full relative items-center;
}
.product-single-details {
  @apply flex flex-col absolute py-5 w-4/5 px-20 lg:-translate-y-20 -translate-y-10 bg-white  shadow-lg rounded;
}
.product-single-group {
  @apply px-10 py-5 w-full;
}
.product-single-title {
  @apply text-lg font-roboto font-semibold;
}
.product-single-data {
  @apply font-roboto text-GTI-BLUE-default;
}

/* Modern Page Header Styles */
.opp-page-header {
  @apply flex flex-col w-full items-center space-y-6 py-8 bg-gradient-to-br from-gray-50 to-white;
}

.opp-page-title {
  @apply flex flex-row items-center space-x-4;
}

.opp-page-title img {
  @apply w-16 h-16 drop-shadow-sm;
}

.opp-page-title h1 {
  @apply text-4xl lg:text-5xl font-bold text-gray-900 tracking-tight;
}

/* Modern Filter Section */
.opp-filters-container {
  @apply flex flex-col lg:flex-row w-full justify-between items-start lg:items-center space-y-4 lg:space-y-0 px-6 lg:px-8 py-6 bg-white border-b border-gray-100;
}

.opp-filters-left {
  @apply flex flex-col sm:flex-row w-full lg:w-auto space-y-3 sm:space-y-0 sm:space-x-4;
}

.opp-filters-right {
  @apply flex w-full lg:w-auto lg:max-w-md;
}

/* Modern Dropdown Styles */
.modern-dropdown-button {
  @apply relative w-full sm:w-48 bg-white border border-gray-300 rounded-xl px-4 py-3 text-left text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 cursor-pointer flex items-center justify-between;
}

.modern-dropdown-content {
  @apply absolute top-full left-0 mt-2 w-full bg-white border border-gray-200 rounded-xl shadow-lg z-20 max-h-60 overflow-auto;
}

.modern-dropdown-item {
  @apply block px-4 py-3 text-sm text-gray-700 hover:bg-GTI-BLUE-default/10 hover:text-GTI-BLUE-default transition-colors duration-150 cursor-pointer border-b border-gray-50 last:border-b-0;
}

/* Modern Search Input */
.modern-search-container {
  @apply relative w-full;
}

.modern-search-input {
  @apply w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 bg-white shadow-sm;
}

.modern-search-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400;
}

/* Container Spacing */
.container-spacing {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-spacing {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-spacing {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Opportunity Card Typography */
.opp-card-date {
  @apply text-xs text-gray-500 font-medium;
}

.opp-card-requirement {
  @apply text-lg font-semibold text-gray-900 line-clamp-2 leading-tight;
}

.opp-card-description {
  @apply text-sm text-gray-600 line-clamp-3 leading-relaxed;
}

.opp-card-company {
  @apply text-xs text-GTI-BLUE-default font-medium uppercase tracking-wide;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive Design Improvements */
@media (max-width: 640px) {
  .modern-dropdown-button {
    @apply w-full text-base py-3;
  }

  .opp-list-main {
    @apply px-4 gap-4;
  }

  .opp-card-main {
    @apply mx-0 hover:transform-none;
  }

  .modern-search-input {
    @apply text-base py-3;
  }

  .opp-card-main {
    @apply min-h-[300px];
  }

  .modern-dropdown-item {
    @apply py-4 text-base;
  }

  .opp-page-title h1 {
    @apply text-3xl;
  }

  .opp-filters-container {
    @apply px-4 py-4;
  }
}

@media (max-width: 768px) {
  .opp-list-main {
    @apply grid-cols-1 gap-4;
    padding: 0 1rem;
  }

  .opp-card-main {
    @apply shadow-md;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .opp-list-main {
    @apply grid-cols-2 gap-5;
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .opp-list-main {
    @apply grid-cols-3 gap-6;
    padding: 0 2rem;
  }
}

@media (min-width: 1280px) {
  .opp-list-main {
    @apply grid-cols-4 gap-6;
    padding: 0 2rem;
  }
}

/* Focus and Accessibility Improvements */
.modern-dropdown-button:focus,
.modern-search-input:focus {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

.opp-card-main:focus,
.opp-card-main:focus-within {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

.modern-dropdown-item:focus {
  @apply bg-GTI-BLUE-default text-white outline-none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .opp-card-main {
    @apply border-2 border-gray-900;
  }

  .modern-dropdown-button,
  .modern-search-input {
    @apply border-2 border-gray-900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .opp-card-main,
  .modern-dropdown-button,
  .modern-search-input,
  .opp-card-img img {
    @apply transition-none;
  }

  .animate-pulse {
    animation: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .opp-card-main:hover {
    @apply transform-none shadow-lg;
  }

  .opp-card-img img {
    @apply transform-none;
  }
}

/* Modern Dropdown Styles */
.modern-dropdown-button {
  @apply flex items-center gap-2 px-4 py-2.5 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:ring-opacity-50 transition-all duration-200;
}

.dropdown-overlay {
  @apply absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50;
}

.modern-dropdown-item {
  @apply w-full text-left px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3 transition-colors duration-200;
}

.modern-create-button {
  @apply flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200;
}

/* Ensure cards are always visible */
.product-list-main .product-card-main {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Override any fade animations that might be hiding cards */
.product-list-main article {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix for width issue - ensure full width always */
.opportunities-container {
  width: 100% !important;
  max-width: none !important;
}

.product-list-main {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
}

/* Prevent any container width constraints during loading */
.min-h-screen {
  width: 100% !important;
}

/* Override any max-width constraints from parent containers */
.min-h-screen > * {
  max-width: none !important;
}

/* Ensure the main page container is always full width */
.bg-gradient-to-br {
  width: 100% !important;
  max-width: none !important;
}

/* Prevent layout shift during component mounting */
.min-h-screen.bg-gradient-to-br {
  width: 100vw !important;
  max-width: 100vw !important;
  overflow-x: hidden;
}

/* Force immediate full width on all child containers */
.min-h-screen.bg-gradient-to-br > * {
  width: 100% !important;
  max-width: none !important;
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slideInDown {
  animation: slideInDown 0.3s ease-out forwards;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-1000 {
  animation-delay: 1s;
}

/* Controls Container Styling */
.controls-container {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.filter-container {
  position: relative;
  z-index: 10000;
}

/* Modern Pagination Button */
.modern-pagination-button {
  @apply bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg inline-flex items-center gap-2 font-medium transition-all duration-200;
}

.modern-pagination-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}
