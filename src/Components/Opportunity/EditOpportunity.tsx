import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Helmet } from "react-helmet";
import { AiOutlineClose } from "react-icons/ai";
import { EDIT_OPPORTUNITY } from "../constants";
import CustomEditor from "../shared/CustomEditor";

const EditOpportunity: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<File[]>([]);
  const [uploadedVideos, setUploadedVideos] = useState<File[]>([]);

  const [formData, setFormData] = useState({
    name: "",
    technologyPartnerRequirement: "",
    description: "",
    sectorId: "",
    subSectorId: "",
  });

  const [description, setDescription] = useState("");

  // Load existing data when component mounts
  useEffect(() => {
    // Simulate loading data based on ID
    // In a real app, this would be an API call
    if (id) {
      // Mock data for demonstration
      const mockData = {
        name: "AI Partnership Opportunity",
        technologyPartnerRequirement:
          "Looking for AI technology partners to enhance our platform capabilities",
        description:
          "<p>We are seeking technology partners with expertise in artificial intelligence and machine learning to collaborate on innovative solutions.</p>",
        sectorId: "technology",
        subSectorId: "ai",
      };

      setFormData({
        name: mockData.name || "",
        technologyPartnerRequirement:
          mockData.technologyPartnerRequirement || "",
        description: mockData.description || "",
        sectorId: mockData.sectorId || "",
        subSectorId: mockData.subSectorId || "",
      });
      setDescription(mockData.description || "");
    }
  }, [id]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log("Updated opportunity data:", {
      ...formData,
      description,
      id,
      images: uploadedImages,
      documents: uploadedDocuments,
      videos: uploadedVideos,
    });
    alert("Opportunity updated successfully!");

    setIsSubmitting(false);
    navigate("/your-opportunities");
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setUploadedImages((prev) => [...prev, ...files]);
  };

  const handleDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setUploadedDocuments((prev) => [...prev, ...files]);
  };

  const handleVideoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setUploadedVideos((prev) => [...prev, ...files]);
  };

  const removeImage = (index: number) => {
    setUploadedImages((prev) => prev.filter((_, i) => i !== index));
  };

  const removeDocument = (index: number) => {
    setUploadedDocuments((prev) => prev.filter((_, i) => i !== index));
  };

  const removeVideo = (index: number) => {
    setUploadedVideos((prev) => prev.filter((_, i) => i !== index));
  };

  const downloadDocument = (file: File) => {
    const url = URL.createObjectURL(file);
    const a = document.createElement("a");
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getCompletionPercentage = () => {
    const fields = [
      formData.name,
      formData.technologyPartnerRequirement,
      description,
      formData.sectorId,
      formData.subSectorId,
    ];
    const filledFields = fields.filter(
      (field) => field && field.trim() !== ""
    ).length;
    return Math.round((filledFields / fields.length) * 100);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
      <Helmet>
        <title>Edit Opportunity - GTI</title>
        <meta
          name="description"
          content="Edit your business opportunity details"
        />
      </Helmet>

      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate("/your-opportunities")}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200"
              >
                <AiOutlineClose className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Edit Opportunity
                </h1>
                <p className="text-sm text-gray-600">
                  Update your business opportunity details
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                <span className="font-medium">
                  {getCompletionPercentage()}%
                </span>{" "}
                complete
              </div>
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-GTI-BLUE-default h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage()}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="pb-6 border-b border-gray-200">
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                Basic Information
              </h2>
              <p className="text-gray-600">
                Update the core details of your opportunity
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 pt-8">
              {/* Opportunity Name */}
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Opportunity Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                  placeholder="Enter opportunity name"
                  required
                />
              </div>

              {/* Technology Partner Requirement */}
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Technology Partner Requirement *
                </label>
                <textarea
                  name="technologyPartnerRequirement"
                  value={formData.technologyPartnerRequirement}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500 resize-none"
                  placeholder="Describe what kind of technology partner you're looking for"
                  required
                />
              </div>

              {/* Description */}
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Detailed Description *
                </label>
                <div className="border border-gray-300 rounded-xl overflow-hidden">
                  <CustomEditor
                    initialContent={description}
                    onChange={setDescription}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* File Uploads Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="pb-6 border-b border-gray-200">
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                Media & Documents
              </h2>
              <p className="text-gray-600">
                Add supporting materials to showcase your opportunity
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 pt-8">
              {/* Images */}
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Images ({uploadedImages.length} uploaded)
                </label>

                {/* Upload Area */}
                <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                  <input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    className="hidden"
                    id="edit-images"
                  />
                  <label htmlFor="edit-images" className="cursor-pointer">
                    <div className="text-gray-500 group-hover:text-blue-600">
                      <svg
                        className="mx-auto h-16 w-16 mb-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                      <p className="text-base font-medium mb-1">
                        Click to upload images
                      </p>
                      <p className="text-sm text-gray-400">
                        PNG, JPG up to 10MB each
                      </p>
                    </div>
                  </label>
                </div>

                {/* Image Previews */}
                {uploadedImages.length > 0 && (
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    {uploadedImages.map((file, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                          <img
                            src={URL.createObjectURL(file)}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center">
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="opacity-0 group-hover:opacity-100 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all duration-200"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </div>
                        <p className="text-xs text-gray-500 mt-1 truncate">
                          {file.name}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Documents */}
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Documents ({uploadedDocuments.length} uploaded)
                </label>

                {/* Upload Area */}
                <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    multiple
                    onChange={handleDocumentUpload}
                    className="hidden"
                    id="edit-documents"
                  />
                  <label htmlFor="edit-documents" className="cursor-pointer">
                    <div className="text-gray-500 group-hover:text-blue-600">
                      <svg
                        className="mx-auto h-16 w-16 mb-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <p className="text-base font-medium mb-1">
                        Click to upload documents
                      </p>
                      <p className="text-sm text-gray-400">
                        PDF, DOC up to 10MB each
                      </p>
                    </div>
                  </label>
                </div>

                {/* Document List */}
                {uploadedDocuments.length > 0 && (
                  <div className="space-y-2 mt-4">
                    {uploadedDocuments.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <svg
                              className="w-8 h-8 text-red-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                              />
                            </svg>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            type="button"
                            onClick={() => downloadDocument(file)}
                            className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-colors duration-200"
                            title="Download"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                              />
                            </svg>
                          </button>
                          <button
                            type="button"
                            onClick={() => removeDocument(index)}
                            className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-full transition-colors duration-200"
                            title="Remove"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Videos */}
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Videos ({uploadedVideos.length} uploaded)
                </label>

                {/* Upload Area */}
                <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                  <input
                    type="file"
                    accept=".mp4,.webm,.mov,.avi"
                    multiple
                    onChange={handleVideoUpload}
                    className="hidden"
                    id="edit-videos"
                  />
                  <label htmlFor="edit-videos" className="cursor-pointer">
                    <div className="text-gray-500 group-hover:text-blue-600">
                      <svg
                        className="mx-auto h-16 w-16 mb-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                      <p className="text-base font-medium mb-1">
                        Click to upload videos
                      </p>
                      <p className="text-sm text-gray-400">
                        MP4, WEBM, MOV up to 50MB each
                      </p>
                    </div>
                  </label>
                </div>

                {/* Video Previews */}
                {uploadedVideos.length > 0 && (
                  <div className="space-y-4 mt-4">
                    {uploadedVideos.map((file, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                          <video
                            src={URL.createObjectURL(file)}
                            controls
                            className="w-full h-full object-cover"
                            preload="metadata"
                          >
                            Your browser does not support the video tag.
                          </video>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button
                            type="button"
                            onClick={() => removeVideo(index)}
                            className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all duration-200 opacity-80 hover:opacity-100"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </div>
                        <div className="mt-2 flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => navigate("/your-opportunities")}
              className="px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-8 py-3 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isSubmitting ? "Updating..." : "Update Opportunity"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditOpportunity;
