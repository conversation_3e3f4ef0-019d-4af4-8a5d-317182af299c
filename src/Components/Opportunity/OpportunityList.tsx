import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import oppbanner from "../../assests/images/product_alt.png";
import { NONE, OPP, oppotunityItemPartialFetched } from "../constants";
import { useNavigate } from "react-router-dom";
import { getOpportunities } from "../../store/actioncreators/opportunityactions";
import { ScreenSpinner } from "../utils/loader";
import RenderHTML from "../utils/RenderHTML";
import biotechnology from "../../assests/images/biotechnology.jpg";
import cleantech from "../../assests/images/cleantech.jpg";
import informationTechnology from "../../assests/images/information-technology.jpg";

const Card = ({ item }: { item: oppotunityItemPartialFetched }) => {
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();

  const handleView = () => {
    navigate(`/opportunity/${item._id}`, {
      state: { product: item },
    });
  };

  const getDefaultImage = (sectorName: string) => {
    if (sectorName === "Biotechnology") {
      return biotechnology;
    } else if (sectorName === "Clean Technology") {
      return cleantech;
    } else {
      return informationTechnology;
    }
  };

  const DOP = new Date(item.createdAt);

  const getSectorColor = (sectorName: string) => {
    switch (sectorName) {
      case "Biotechnology":
        return "bg-green-100 text-green-800";
      case "Clean Technology":
        return "bg-blue-100 text-blue-800";
      case "Information Technology":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <article
      className="flex flex-col justify-start duration-150 border md:pb-2 shadow-lg hover:shadow-lg hover:shadow-GTI-BLUE-default space-y-1 rounded-xl cursor-pointer group transform hover:-translate-y-1 transition-all bg-white"
      onClick={handleView}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleView();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${
        item.technologyPartnerRequirement || "opportunity"
      }`}
    >
      {/* Image Section with Overlay */}
      <div className="relative">
        <img
          src={
            item.image
              ? item.image
              : item.company.logo
              ? item.company.logo
              : getDefaultImage(item.sectors.name)
          }
          className="w-full h-48 object-cover rounded-t-xl"
          alt={item.technologyPartnerRequirement || "Opportunity"}
          loading="lazy"
        />

        {/* Sector Badge */}
        {item.sectors?.name && (
          <div className="absolute top-3 left-3">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSectorColor(
                item.sectors.name
              )}`}
            >
              {item.sectors.name}
            </span>
          </div>
        )}

        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-t-xl flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="bg-white rounded-full p-3 shadow-lg">
              <svg
                className="w-6 h-6 text-GTI-BLUE-default"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="p-6 flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-roboto text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
              {item?.technologyPartnerRequirement?.replace(
                /(<([^>]+)>)/gi,
                ""
              ) || "Technology Partnership Opportunity"}
            </h3>
            {item.company?.name && (
              <p className="text-sm text-gray-600 mt-1 font-medium">
                {item.company.name}
              </p>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="flex-1 mb-4">
          <div className="text-sm text-gray-600 line-clamp-3">
            <RenderHTML
              html={
                item.description
                  ? item.description.length > 120
                    ? item.description.substring(0, 120) + "..."
                    : item.description
                  : "No description available"
              }
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center text-xs text-gray-500">
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            {DOP.toLocaleString("default", {
              month: "short",
              day: "2-digit",
              year: "numeric",
            })}
          </div>

          <div className="flex items-center text-GTI-BLUE-default text-sm font-medium">
            View Details
            <svg
              className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      </div>
    </article>
  );
};

const OpportunityList = ({
  skip,
  limit,
  secId,
  subSecId,
  search,
}: {
  skip: string;
  limit: string;
  subSecId: string;
  secId: string;
  search: string;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  const opportunity: OPP = useSelector((state: STATE) => state.OPP.OPP);

  useEffect(() => {
    dispatch(getOpportunities(skip, limit, secId, subSecId, search));
  }, [secId, subSecId, skip, search]);

  return (
    <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 pb-16">
      {spinner.SPINNER ? (
        <ScreenSpinner />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
          {opportunity.OPP_LIST?.opportunities &&
            opportunity.OPP_LIST?.opportunities.map(
              (item: oppotunityItemPartialFetched, id: number) => {
                return <Card item={item} key={id} />;
              }
            )}
        </div>
      )}
    </div>
  );
};
export default OpportunityList;
