import React, { Dispatch, useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { useDispatch, useSelector } from "react-redux";
import { Form, Formik } from "formik";
import { useDetectClickOutside } from "react-detect-click-outside";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { Helmet } from "react-helmet";

import globe from "../../assests/home/<USER>";

import {
  CONTENT_TYPE,
  CONTENT_TYPE_DOC,
  CREATE_OPPORTUNITY,
  FILE_PATH,
  FILE_TYPE,
  LIMIT,
  oppotunityItem,
  presignedData,
  sectorItem,
  SKIP,
  subsectorItem,
  title,
  metaData,
} from "../constants";
import { getSector } from "../../store/actioncreators/sectoractions";
import { getSubSector } from "../../store/actioncreators/sub-sectoractions";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import YourOpportunityList from "./YourOpportunityList";
import { createOppotunity } from "../../store/actioncreators/opportunityactions";
import { oppSchema } from "../validations/oppValidations";
import { notify } from "../../utils";
import { PROFILE_TYPES } from "../../shared/enum";
import { RequestMethods } from "../../shared/RequestMethods";
import SuccessModal from "./SuccessModal";
import "./style.css";
import CustomEditor from "../shared/CustomEditor";

interface MyFormValues {
  name: string;
  tech_require: string;
  description: string;
  sectorId: string;
  subSectorId: string;
}

type files = {
  image: Boolean;
  document: Boolean;
  imageFile: File[];
  documentFiles: File[];
};

const OppModal = ({
  changeModalState,
  handleSuccessModal,
}: {
  changeModalState: () => void;
  handleSuccessModal: (isOpen: boolean, state: string, message: string) => void;
}) => {
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );

  const initialValues: MyFormValues = {
    name: "",
    tech_require: "",
    description: "",
    sectorId: sectorlist.SECTOR_LIST[0]._id,
    subSectorId: subsectorlist.SUB_SECTOR_LIST[0]._id,
  };

  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();
  const user: USER = useSelector((state: STATE) => state.USER.USER);
  useEffect(() => {
    if (user.id && user.userType === PROFILE_TYPES.GENERAL_SUBSCRIBER) {
      notify("Unauthorized", "error");
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const [files, setFiles] = useState<files>({
    image: false,
    document: false,
    imageFile: [],
    documentFiles: [],
  });

  const handleImage = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files || [];
    if (!fileList) return;

    const images = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      images.push(fileList[i]);
    }

    setFiles({ ...files, imageFile: images, image: false });
  };

  const handleDocuments = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files || [];
    if (!fileList) return;

    const documentFiles = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      documentFiles.push(fileList[i]);
    }

    setFiles({ ...files, documentFiles, document: false });
  };

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = "error";
        dispatch(failToast());
      });

    return result;
  };

  const postLogo = async (signed: string, imageFile: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: imageFile,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const postDocument = async (signed: string, file: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE_DOC,
        "Access-Control-Allow-Origin": true,
      },
      data: file,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const handleCreate = async (values: MyFormValues) => {
    try {
      handleSuccessModal(true, "LOADING", "");
      if (!files.imageFile) {
        return setFiles({ ...files, image: true });
      }
      setFiles({ ...files, document: false, image: false });

      let opportunityImages: string[] = [];
      let documents: string[] = [];

      for (const file of files.imageFile) {
        const signedLogoData: presignedData = {
          fileName: file.name ?? values.name,
          filePath: FILE_PATH.PRODUCTS_IMAGE,
          fileType: FILE_TYPE.PNG,
        };
        let imageUrl = await getPresigned(signedLogoData);
        await postLogo(imageUrl, file);
        opportunityImages.push(imageUrl.split("?")[0]);
      }

      for (const file of files.documentFiles) {
        const signedDocumentData: presignedData = {
          fileName: file.name || values.name,
          filePath: FILE_PATH.COMPANY_DOCS,
          fileType: FILE_TYPE.PDF,
        };
        let documentUrl = await getPresigned(signedDocumentData);
        await postDocument(documentUrl, file);
        documents.push(documentUrl.split("?")[0]);
      }

      const data: oppotunityItem = {
        technologyPartnerRequirement: values.tech_require,
        description: values.description,
        documents,
        images: opportunityImages,
        sectorId: values.sectorId,
        subSectorId: values.subSectorId,
        name: values.name,
      };

      dispatch(createOppotunity(data));
      handleSuccessModal(
        true,
        "SUCCESS",
        "Opportunity has been created successfully, it will be reviewed and approved by Admin."
      );
      changeModalState();
    } catch (error) {
      handleSuccessModal(
        true,
        "ERROR",
        "There was an error while creating the opoortunity"
      );
      notify("Failed to create opoortunity!", "error");
    }
  };

  useEffect(() => {
    window.document.body.style.overflow = "hidden";
    return () => {
      window.document.body.style.overflow = "auto";
    };
  }, []);

  const content = (
    <div className="z-10 pb-[200px] pt-4 fixed w-full h-screen bg-slate-700 bg-opacity-70 top-0 left-0 flex justify-center overflow-auto">
      <div className="product-modal-main relative">
        <div className="flex">
          <h4 className="text-lg font-roboto">Create</h4>
          <button
            onClick={() => {
              changeModalState();
            }}
            className="absolute right-0 top-0 font-bold hover:text-red-500 duration-300 border border-slate-100 px-3 py-1 rounded"
          >
            X
          </button>
        </div>
        <Formik
          initialValues={initialValues}
          validationSchema={oppSchema}
          onSubmit={(values) => handleCreate(values)}
        >
          {({ handleChange, setFieldValue, handleSubmit, errors, values }) => (
            <>
              <Form className="flex flex-col w-full space-y-4 justify-center items-center">
                <div className="flex flex-col w-full space-x-2 relative">
                  <div className="flex flex-col w-full space-x-2 relative">
                    <div className="relative mb-4 ">
                      <input
                        type="text"
                        name="name"
                        id="floating_name"
                        onChange={(e) => setFieldValue("name", e.target.value)}
                        className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                        placeholder=" "
                      />
                      <label
                        htmlFor="floating_name"
                        className="absolute text-sm text-gray-500  duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white  px-2 peer-focus:px-2 peer-focus:text-blue-600  peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                      >
                        Name
                      </label>
                    </div>
                  </div>
                  <div className="relative">
                    <textarea
                      onChange={(e) =>
                        setFieldValue("tech_require", e.target.value)
                      }
                      id="floating_outlined"
                      className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" "
                    />
                    <label
                      htmlFor="floating_outlined"
                      className="absolute text-sm text-gray-500  duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white  px-2 peer-focus:px-2 peer-focus:text-blue-600  peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                    >
                      Technology Requirement
                    </label>
                  </div>
                  {errors.tech_require && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.tech_require}
                    </p>
                  )}
                </div>
                <div className="flex flex-col w-full space-x-2 relative">
                  <div className="relative">
                    <CustomEditor
                      onChange={(content: string) => {
                        setFieldValue("description", content);
                      }}
                    />
                    <label
                      htmlFor="floating_outlined"
                      className="absolute text-sm text-gray-500  duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white  px-2 peer-focus:px-2 peer-focus:text-blue-600  peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
                    >
                      Opportunity Description
                    </label>
                  </div>
                  {errors.description && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.description}
                    </p>
                  )}
                </div>

                <div className="flex flex-col w-full">
                  <div className="flex flex-row w-full space-x-5 items-center">
                    <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
                      Sector Type:
                    </h3>
                    <select
                      onChange={(e) =>
                        setFieldValue("sectorId", e.target.value)
                      }
                      defaultValue={values.sectorId}
                      className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                    >
                      {sectorlist.SECTOR_LIST.map((item: sectorItem, id) => {
                        return <option value={item._id}>{item.name}</option>;
                      })}
                    </select>
                  </div>
                  {errors.sectorId && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.sectorId}
                    </p>
                  )}
                </div>
                <div className="flex flex-col w-full">
                  <div className="flex flex-row w-full space-x-5 items-center">
                    <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
                      Sub Sector Type:
                    </h3>
                    <select
                      onChange={(e) =>
                        setFieldValue("subsecId", e.target.value)
                      }
                      defaultValue={values.subSectorId}
                      className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                    >
                      {subsectorlist.SUB_SECTOR_LIST.map(
                        (item: subsectorItem, id) => {
                          return <option value={item._id}>{item.name}</option>;
                        }
                      )}
                    </select>
                  </div>
                  {errors.subSectorId && (
                    <p
                      id="filled_error_help"
                      className="mt-2 text-xs text-red-600 dark:text-red-400"
                    >
                      {errors.subSectorId}
                    </p>
                  )}
                </div>

                <div className="flex flex-col w-full">
                  <label
                    className="block mb-2 text-sm font-medium text-gray-900"
                    htmlFor="logo"
                  >
                    Upload Opportunity Image
                    <span className="text-red-500 text-xs">(.png only)</span>
                  </label>
                  <input
                    onChange={handleImage}
                    accept=".png"
                    type="file"
                    id="logo"
                    aria-label="company-logo"
                    className="modal-input"
                    placeholder="Click to upload Company's Logo"
                    multiple
                    max={3}
                  />
                  <p
                    id="filled_error_help"
                    className={
                      "mt-2 text-xs text-red-600 dark:text-red-400 " +
                      (!files.image ? "hidden" : "")
                    }
                  >
                    {"Please upload opportunity Image"}
                  </p>
                </div>
                <div className="flex flex-col w-full">
                  <label
                    className="block mb-2 text-sm font-medium text-gray-900"
                    htmlFor="documents"
                  >
                    Upload Opportunity Documents
                    <span className="text-red-500 text-xs">(.pdf only)</span>
                  </label>
                  <input
                    onChange={handleDocuments}
                    accept=".pdf"
                    type="file"
                    id="documents"
                    aria-label="company-documents"
                    className="modal-input"
                    placeholder="Click to upload Document"
                    multiple
                    max={3}
                  />
                  <p
                    id="filled_error_help"
                    className={
                      "mt-2 text-xs text-red-600 dark:text-red-400 " +
                      (!files.document ? "hidden" : "")
                    }
                  >
                    {"Please upload opportunity documents"}
                  </p>
                </div>
                <button
                  type="submit"
                  onClick={() => handleSubmit}
                  className="button active"
                >
                  Create
                </button>
              </Form>
            </>
          )}
        </Formik>
      </div>
    </div>
  );
  return ReactDOM.createPortal(content, document.body);
};

const YourOpportunites = () => {
  let [successModal, setSuccessModal] = useState<boolean>(false);
  const [state, setState] = useState("LOADING");
  const [message, setMessage] = useState("");

  const opp: OPP = useSelector((state: STATE) => state.OPP.OPP);
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();
  const [oppModal, setModel] = useState(false);

  const [page, setPage] = useState({
    skip: SKIP,
    limit: LIMIT,
  });

  const [type, setType] = useState({
    drop: false,
    selected: "Approved",
    id: 0,
    total: opp.TOTAL_APPROVED,
  });

  const ref1 = useDetectClickOutside({
    onTriggered: () => setType({ ...type, drop: false }),
  });

  const [sector, setSector] = useState({
    drop: false,
    selected: "",
    id: "",
  });
  const [subSector, setSubSector] = useState({
    drop: false,
    selected: "",
    id: "",
  });

  const fetchData = (value: number) => {
    let final = +page.skip + value < 0 ? +page.skip : +page.skip + value;
    setPage({ skip: final.toString(), limit: page.limit });
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    dispatch(getSector());
    dispatch(getSubSector());
    setSector({
      ...sector,
      selected: "All Sectors",
      id: "",
    });
    setSubSector({
      ...subSector,
      selected: "All Sub-Sectors",
      id: "",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const changeModalState = async () => {
    setModel(!oppModal);
  };

  const handleSuccessModal = (
    isOpen: boolean,
    state: string,
    message: string
  ) => {
    setSuccessModal(isOpen);
    setState(state);
    setMessage(message);
  };

  const handleType = (index: number) => {
    if (index === 0) {
      setType({
        ...type,
        selected: "Approved",
        id: 0,
        total: opp.TOTAL_APPROVED,
        drop: false,
      });
    } else if (index === 1) {
      setType({
        ...type,
        selected: "Pending",
        id: 1,
        total: opp.TOTAL_PENDING,
        drop: false,
      });
    } else {
      setType({
        ...type,
        selected: "Rejected",
        id: 2,
        total: opp.TOTAL_REJECTED,
        drop: false,
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/20 to-indigo-50/30">
      <Helmet>
        <title>{title.YOUR_OPPORTUNITIES}</title>
        <meta
          name="description"
          key="description"
          content={metaData.YOUR_OPPORTUNITIES}
        />
        <meta name="title" key="title" content="Your Opportunities" />
        <meta property="og:title" content="Your Opportunities" />
        <meta property="og:description" content={metaData.YOUR_OPPORTUNITIES} />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/your-opportunities`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Your Opportunities" />
        <meta
          name="twitter:description"
          content={metaData.YOUR_OPPORTUNITIES}
        />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content="Your Opportunities" />
      </Helmet>

      {/* Modern Header Section */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="flex mb-6" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <a
                  href="/"
                  className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                  </svg>
                  Home
                </a>
              </li>
              <li>
                <div className="flex items-center">
                  <svg
                    className="w-6 h-6 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                    Your Opportunities
                  </span>
                </div>
              </li>
            </ol>
          </nav>

          {/* Header Content */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center mb-6 lg:mb-0">
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 rounded-2xl shadow-lg mr-4">
                <svg
                  className="w-10 h-10 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"
                  />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 font-roboto">
                  Your Opportunities
                </h1>
                <p className="text-gray-600 mt-1 font-roboto">
                  Manage and track your opportunity submissions and approvals
                </p>
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-GTI-BLUE-default">
                  {opp.TOTAL_APPROVED || 0}
                </div>
                <div className="text-sm text-gray-500">Approved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-500">
                  {opp.TOTAL_PENDING || 0}
                </div>
                <div className="text-sm text-gray-500">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">
                  {opp.TOTAL_REJECTED || 0}
                </div>
                <div className="text-sm text-gray-500">Rejected</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Simplified Controls Section */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8 relative z-[100]">
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 controls-container">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
            {/* Status Filter */}
            <div className="relative z-[10000] filter-container" ref={ref1}>
              <button
                className="group flex items-center gap-3 px-6 py-3 bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300 rounded-xl transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onClick={() => setType({ ...type, drop: !type.drop })}
              >
                <div className="flex items-center gap-2">
                  <div
                    className={`w-3 h-3 rounded-full ${
                      type.selected === "Approved"
                        ? "bg-emerald-500"
                        : type.selected === "Pending"
                        ? "bg-amber-500"
                        : "bg-red-500"
                    }`}
                  ></div>
                  <span className="font-medium text-gray-700">
                    {type.selected}
                  </span>
                </div>
                <svg
                  className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
                    type.drop ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {type.drop && (
                <div
                  className="absolute top-full left-0 mt-2 w-80 bg-white rounded-2xl shadow-2xl border border-gray-100 py-3 animate-slideInDown dropdown-overlay"
                  style={{
                    position: "absolute",
                    zIndex: 99999,
                    backgroundColor: "white",
                    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                    transform: "translateZ(0)",
                  }}
                >
                  <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100 mb-2">
                    Filter by Status
                  </div>
                  {[
                    {
                      label: "Approved",
                      value: 0,
                      color: "bg-emerald-500",
                      description: "Opportunities approved by admin",
                      icon: "✓",
                    },
                    {
                      label: "Pending",
                      value: 1,
                      color: "bg-amber-500",
                      description: "Awaiting admin review",
                      icon: "⏳",
                    },
                    {
                      label: "Rejected",
                      value: 2,
                      color: "bg-red-500",
                      description: "Opportunities that need revision",
                      icon: "✗",
                    },
                  ].map((item) => (
                    <button
                      key={item.value}
                      className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between group transition-all duration-150 rounded-lg mx-2"
                      onClick={() => handleType(item.value)}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-3 h-3 rounded-full ${item.color} group-hover:scale-110 transition-transform duration-200`}
                        ></div>
                        <div>
                          <div className="font-medium text-gray-900 group-hover:text-blue-600">
                            {item.label}
                          </div>
                          <div className="text-xs text-gray-500">
                            {item.description}
                          </div>
                        </div>
                      </div>
                      <span className="text-lg opacity-50 group-hover:opacity-100 transition-opacity duration-200">
                        {item.icon}
                      </span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Create Opportunity Button */}
            <button
              type="button"
              className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
              onClick={() => navigate(CREATE_OPPORTUNITY)}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 rounded-2xl transition-opacity duration-300"></div>
              <span className="relative flex items-center gap-2">
                <svg
                  className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Create Opportunity
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Opportunities List Container */}
      <div
        className="opportunities-container w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 pb-12"
        style={{ width: "100%", maxWidth: "none" }}
      >
        <YourOpportunityList
          type={type.id}
          skip={page.skip}
          limit={page.limit}
          secId={sector.id}
          subsecId={subSector.id}
        />

        {/* Modern Pagination */}
        <div className="flex justify-center items-center mt-12 gap-2">
          <button
            disabled={page.skip === "0"}
            onClick={() => fetchData(-1)}
            className="modern-pagination-button disabled:opacity-50 disabled:cursor-not-allowed bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg inline-flex items-center gap-2 font-medium transition-all duration-200"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Previous
          </button>

          <div className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium">
            Page {+page.skip + 1}
          </div>

          <button
            disabled={(+page.skip + 1) * +page.limit >= type.total}
            onClick={() => fetchData(1)}
            className="modern-pagination-button disabled:opacity-50 disabled:cursor-not-allowed bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg inline-flex items-center gap-2 font-medium transition-all duration-200"
          >
            Next
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      </div>

      {oppModal && (
        <OppModal
          changeModalState={changeModalState}
          handleSuccessModal={handleSuccessModal}
        />
      )}
      {successModal && (
        <SuccessModal
          state={state}
          message={message}
          show={successModal}
          toggle={handleSuccessModal}
        />
      )}
    </div>
  );
};
export default YourOpportunites;
