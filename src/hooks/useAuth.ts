import { useSelector } from "react-redux";
import { STORAGE_KEY } from "../Components/constants";

export const useAuth = () => {
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);

  // Check if user is authenticated based on Redux state and localStorage
  const isAuthenticated = () => {
    const token = localStorage.getItem(STORAGE_KEY);
    return token && currentUser.admin !== -1 && currentUser.id !== "-1";
  };

  // Get authentication token from localStorage
  const getToken = () => {
    const gtiData = localStorage.getItem(STORAGE_KEY);
    return gtiData ? gtiData.split(" ")[0] : null;
  };

  // Check if token exists in localStorage
  const hasToken = () => {
    const token = getToken();
    return token !== null && token !== "";
  };

  // Get user ID from localStorage
  const getUserId = () => {
    const gtiData = localStorage.getItem(STORAGE_KEY);
    return gtiData ? gtiData.split(" ")[2] : null;
  };

  // Get user email from localStorage
  const getUserEmail = () => {
    const gtiData = localStorage.getItem(STORAGE_KEY);
    return gtiData ? gtiData.split(" ")[1] : null;
  };

  return {
    isAuthenticated: isAuthenticated(),
    hasToken: hasToken(),
    getToken,
    getUserId,
    getUserEmail,
    currentUser,
  };
};
