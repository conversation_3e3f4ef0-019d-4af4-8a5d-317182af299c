import axios from "axios";
import { STORAGE_KEY } from "../Components/constants";

export const AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_BASE_API,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token to requests
AxiosInstance.interceptors.request.use(
  (config) => {
    const gtiData = localStorage.getItem(STORAGE_KEY);
    if (gtiData) {
      const token = gtiData.split(" ")[0];
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token expiration
AxiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized responses (token expired/invalid)
    if (error.response?.status === 401) {
      // Clear invalid token from localStorage
      localStorage.removeItem(STORAGE_KEY);
      // Optionally redirect to login or show login modal
      // This can be handled by the components using the API
    }
    return Promise.reject(error);
  }
);
