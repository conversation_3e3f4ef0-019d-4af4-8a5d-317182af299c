import { STORAGE_KEY } from "../Components/constants";

/**
 * Check if user has a valid authentication token
 */
export const hasValidToken = (): boolean => {
  const gtiData = localStorage.getItem(STORAGE_KEY);
  if (!gtiData) return false;
  
  const token = gtiData.split(" ")[0];
  return token !== null && token !== "" && token !== "undefined";
};

/**
 * Get authentication token from localStorage
 */
export const getAuthToken = (): string | null => {
  const gtiData = localStorage.getItem(STORAGE_KEY);
  if (!gtiData) return null;
  
  const token = gtiData.split(" ")[0];
  return token && token !== "undefined" ? token : null;
};

/**
 * Check if an API call should be made based on authentication requirements
 * @param requiresAuth - Whether the API call requires authentication
 * @returns boolean indicating if the API call should proceed
 */
export const shouldMakeApiCall = (requiresAuth: boolean = true): boolean => {
  if (!requiresAuth) return true;
  return hasValidToken();
};

/**
 * Create axios config with authentication headers if token exists
 */
export const createAuthConfig = (additionalConfig: any = {}) => {
  const token = getAuthToken();
  const config = {
    ...additionalConfig,
    headers: {
      "Content-Type": "application/json",
      ...additionalConfig.headers,
    },
  };

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  return config;
};
